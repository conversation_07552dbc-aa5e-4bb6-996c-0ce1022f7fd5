/* SAM - نظام إدارة شؤون الموظفين */
/* Custom CSS Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* Arabic Font */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    direction: rtl;
    text-align: right;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), #4c84ff);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    color: white;
}

.loading-content {
    text-align: center;
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Login Screen */
.login-screen {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), #4c84ff);
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-card .form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    padding: 0.75rem;
    transition: var(--transition);
}

.login-card .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.login-card .btn {
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-weight: 600;
    transition: var(--transition);
}

/* Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Forms */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #4c84ff);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card .stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Employee Card */
.employee-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.employee-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.employee-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

/* Modal */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
}

.modal-header {
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
}

/* Sidebar (if needed) */
.sidebar {
    background-color: white;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.sidebar .nav-link {
    color: var(--dark-color);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin-bottom: 0.25rem;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .stats-card {
        text-align: center;
    }
    
    .employee-card {
        margin-bottom: 1rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .modal,
    .loading-screen,
    .login-screen {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0b5ed7;
}
