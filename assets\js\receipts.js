/**
 * SAM - نظام إدارة شؤون الموظفين
 * Payment Receipts Module
 * وحدة إيصالات القبض
 */

class ReceiptsManager {
    constructor() {
        this.currentMonth = new Date().toISOString().slice(0, 7);
        this.selectedEmployee = '';
    }

    render() {
        if (!window.authManager.hasPermission('payroll')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadReceiptsData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-receipt me-2"></i>
                            إيصالات القبض
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-primary" id="generateReceiptsBtn">
                                <i class="fas fa-plus me-2"></i>
                                إنشاء إيصالات
                            </button>
                            <button class="btn btn-success" id="generateSummaryReceiptBtn">
                                <i class="fas fa-file-invoice me-2"></i>
                                إيصال إجمالي
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <label class="form-label">الشهر</label>
                    <input type="month" class="form-control" id="monthFilter" value="${this.currentMonth}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">الموظف</label>
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary flex-fill" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-outline-info" id="printAllReceiptsBtn">
                            <i class="fas fa-print"></i> طباعة الكل
                        </button>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalReceipts">0</h4>
                                    <p class="mb-0">عدد الإيصالات</p>
                                </div>
                                <i class="fas fa-receipt fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalAmount">0</h4>
                                    <p class="mb-0">إجمالي المبلغ</p>
                                </div>
                                <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="paidReceipts">0</h4>
                                    <p class="mb-0">مدفوعة</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="pendingReceipts">0</h4>
                                    <p class="mb-0">في الانتظار</p>
                                </div>
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Receipts Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الإيصالات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الإيصال</th>
                                    <th>الموظف</th>
                                    <th>الشهر</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="receiptsTableBody">
                                <!-- Receipts will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Receipt Modal -->
            <div class="modal fade" id="receiptModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إيصال قبض راتب</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="receiptContent">
                            <!-- Receipt content will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="printReceiptBtn">طباعة</button>
                            <button type="button" class="btn btn-success" id="markAsPaidBtn">تأكيد الدفع</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Receipt Modal -->
            <div class="modal fade" id="summaryReceiptModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">الإيصال الإجمالي للرواتب</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="summaryReceiptContent">
                            <!-- Summary receipt content will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="printSummaryReceiptBtn">طباعة</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Generate receipts
        document.getElementById('generateReceiptsBtn').addEventListener('click', () => {
            this.generateReceipts();
        });

        // Generate summary receipt
        document.getElementById('generateSummaryReceiptBtn').addEventListener('click', () => {
            this.generateSummaryReceipt();
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadReceiptsData();
        });

        // Print all receipts
        document.getElementById('printAllReceiptsBtn').addEventListener('click', () => {
            this.printAllReceipts();
        });

        // Print receipt
        document.getElementById('printReceiptBtn').addEventListener('click', () => {
            this.printCurrentReceipt();
        });

        // Print summary receipt
        document.getElementById('printSummaryReceiptBtn').addEventListener('click', () => {
            this.printCurrentSummaryReceipt();
        });

        // Mark as paid
        document.getElementById('markAsPaidBtn').addEventListener('click', () => {
            this.markReceiptAsPaid();
        });

        // Month filter
        document.getElementById('monthFilter').addEventListener('change', (e) => {
            this.currentMonth = e.target.value;
            this.loadReceiptsData();
        });

        // Employee filter
        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadReceiptsData();
        });
    }

    loadReceiptsData() {
        this.loadEmployeeOptions();
        this.loadReceiptsTable();
        this.updateSummaryCards();
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const select = document.getElementById('employeeFilter');
        
        select.innerHTML = '<option value="">جميع الموظفين</option>';
        employees.forEach(emp => {
            select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
        });
    }

    loadReceiptsTable() {
        let receipts = Database.getAll('receipts') || [];
        
        // Apply filters
        if (this.currentMonth) {
            receipts = receipts.filter(receipt => receipt.month === this.currentMonth);
        }
        
        if (this.selectedEmployee) {
            receipts = receipts.filter(receipt => receipt.employee_id === this.selectedEmployee);
        }
        
        this.renderReceiptsTable(receipts);
    }

    renderReceiptsTable(receipts) {
        const tbody = document.getElementById('receiptsTableBody');
        const employees = Database.getEmployees();

        if (receipts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد إيصالات</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = receipts.map(receipt => {
            const employee = employees.find(emp => emp.id === receipt.employee_id);
            const statusBadge = this.getStatusBadge(receipt.status);

            return `
                <tr>
                    <td>
                        <span class="badge bg-secondary">${receipt.receipt_number}</span>
                    </td>
                    <td>
                        <div class="fw-bold">${employee?.name || 'غير معروف'}</div>
                        <small class="text-muted">${employee?.employee_number || ''}</small>
                    </td>
                    <td>${this.formatMonth(receipt.month)}</td>
                    <td class="fw-bold">${window.samApp.formatCurrency(receipt.amount)}</td>
                    <td>${window.samApp.formatDate(receipt.created_at)}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary view-receipt"
                                    data-receipt-id="${receipt.id}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-info print-receipt"
                                    data-receipt-id="${receipt.id}">
                                <i class="fas fa-print"></i>
                            </button>
                            ${receipt.status === 'pending' ? `
                            <button class="btn btn-outline-success mark-paid"
                                    data-receipt-id="${receipt.id}">
                                <i class="fas fa-check"></i>
                            </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View receipt
        document.querySelectorAll('.view-receipt').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const receiptId = e.target.closest('.view-receipt').dataset.receiptId;
                this.viewReceipt(receiptId);
            });
        });

        // Print receipt
        document.querySelectorAll('.print-receipt').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const receiptId = e.target.closest('.print-receipt').dataset.receiptId;
                this.printReceipt(receiptId);
            });
        });

        // Mark as paid
        document.querySelectorAll('.mark-paid').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const receiptId = e.target.closest('.mark-paid').dataset.receiptId;
                this.markAsPaid(receiptId);
            });
        });
    }

    getStatusBadge(status) {
        const statusMap = {
            'pending': { class: 'warning', text: 'في الانتظار' },
            'paid': { class: 'success', text: 'مدفوع' },
            'cancelled': { class: 'danger', text: 'ملغي' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    updateSummaryCards() {
        const receipts = Database.getAll('receipts') || [];
        const filteredReceipts = receipts.filter(receipt => {
            let match = true;
            if (this.currentMonth) match = match && receipt.month === this.currentMonth;
            if (this.selectedEmployee) match = match && receipt.employee_id === this.selectedEmployee;
            return match;
        });

        const totalReceipts = filteredReceipts.length;
        const totalAmount = filteredReceipts.reduce((sum, receipt) => sum + (receipt.amount || 0), 0);
        const paidReceipts = filteredReceipts.filter(r => r.status === 'paid').length;
        const pendingReceipts = filteredReceipts.filter(r => r.status === 'pending').length;

        document.getElementById('totalReceipts').textContent = totalReceipts;
        document.getElementById('totalAmount').textContent = window.samApp.formatCurrency(totalAmount);
        document.getElementById('paidReceipts').textContent = paidReceipts;
        document.getElementById('pendingReceipts').textContent = pendingReceipts;
    }

    generateReceipts() {
        const payrolls = Database.getAll('payroll') || [];
        const monthPayrolls = payrolls.filter(p => p.month === this.currentMonth);

        if (monthPayrolls.length === 0) {
            window.samApp.showAlert('لا توجد كشوف رواتب للشهر المحدد', 'warning');
            return;
        }

        let generatedCount = 0;
        monthPayrolls.forEach(payroll => {
            // Check if receipt already exists
            const existingReceipts = Database.getAll('receipts') || [];
            const exists = existingReceipts.some(r =>
                r.employee_id === payroll.employee_id && r.month === payroll.month
            );

            if (!exists) {
                const receiptData = {
                    receipt_number: this.generateReceiptNumber(),
                    employee_id: payroll.employee_id,
                    month: payroll.month,
                    amount: payroll.net_salary,
                    payroll_details: {
                        basic_salary: payroll.basic_salary,
                        allowances: payroll.total_allowances,
                        deductions: payroll.total_deductions,
                        gross_salary: payroll.gross_salary,
                        net_salary: payroll.net_salary
                    },
                    status: 'pending',
                    created_at: new Date().toISOString()
                };

                Database.create('receipts', receiptData);
                generatedCount++;
            }
        });

        if (generatedCount > 0) {
            window.samApp.showAlert(`تم إنشاء ${generatedCount} إيصال بنجاح`, 'success');
            this.loadReceiptsData();
        } else {
            window.samApp.showAlert('جميع الإيصالات موجودة مسبقاً', 'info');
        }
    }

    generateReceiptNumber() {
        const receipts = Database.getAll('receipts') || [];
        const currentYear = new Date().getFullYear();
        const yearReceipts = receipts.filter(r => r.created_at.startsWith(currentYear.toString()));
        const nextNumber = yearReceipts.length + 1;
        return `REC-${currentYear}-${nextNumber.toString().padStart(4, '0')}`;
    }

    generateSummaryReceipt() {
        const modal = new bootstrap.Modal(document.getElementById('summaryReceiptModal'));
        const content = document.getElementById('summaryReceiptContent');

        const payrolls = Database.getAll('payroll') || [];
        const monthPayrolls = payrolls.filter(p => p.month === this.currentMonth);

        if (monthPayrolls.length === 0) {
            window.samApp.showAlert('لا توجد كشوف رواتب للشهر المحدد', 'warning');
            return;
        }

        content.innerHTML = this.generateSummaryReceiptHTML(monthPayrolls);
        modal.show();
    }

    generateSummaryReceiptHTML(payrolls) {
        const settings = Database.getSettings();
        const employees = Database.getEmployees();

        const totals = payrolls.reduce((acc, payroll) => {
            acc.basicSalary += payroll.basic_salary || 0;
            acc.allowances += payroll.total_allowances || 0;
            acc.deductions += payroll.total_deductions || 0;
            acc.netSalary += payroll.net_salary || 0;
            return acc;
        }, { basicSalary: 0, allowances: 0, deductions: 0, netSalary: 0 });

        return `
            <div style="direction: rtl; font-family: Arial, sans-serif;">
                <div class="text-center mb-4">
                    <h2>${settings.company.name}</h2>
                    <p>${settings.company.address}</p>
                    <p>هاتف: ${settings.company.phone} | بريد إلكتروني: ${settings.company.email}</p>
                    <hr>
                    <h3>إيصال إجمالي لصرف الرواتب</h3>
                    <p>شهر: ${this.formatMonth(this.currentMonth)}</p>
                    <p>تاريخ الإصدار: ${window.samApp.formatDate(new Date().toISOString())}</p>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>ملخص الرواتب:</h5>
                        <table class="table table-bordered">
                            <tr><td>عدد الموظفين:</td><td class="fw-bold">${payrolls.length}</td></tr>
                            <tr><td>إجمالي الرواتب الأساسية:</td><td>${window.samApp.formatCurrency(totals.basicSalary)}</td></tr>
                            <tr><td>إجمالي البدلات:</td><td>${window.samApp.formatCurrency(totals.allowances)}</td></tr>
                            <tr><td>إجمالي الخصومات:</td><td>${window.samApp.formatCurrency(totals.deductions)}</td></tr>
                            <tr class="table-success"><td><strong>صافي المبلغ المصروف:</strong></td><td class="fw-bold">${window.samApp.formatCurrency(totals.netSalary)}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>تفاصيل الصرف:</h5>
                        <p><strong>طريقة الدفع:</strong> تحويل بنكي / نقداً</p>
                        <p><strong>رقم الإيصال:</strong> ${this.generateReceiptNumber()}</p>
                        <p><strong>المسؤول عن الصرف:</strong> _______________</p>
                        <p><strong>التوقيع:</strong> _______________</p>
                        <p><strong>التاريخ:</strong> ${window.samApp.formatDate(new Date().toISOString())}</p>
                    </div>
                </div>

                <h5>تفاصيل الموظفين:</h5>
                <table class="table table-bordered table-sm">
                    <thead class="table-dark">
                        <tr>
                            <th>الموظف</th>
                            <th>الرقم الوظيفي</th>
                            <th>الراتب الأساسي</th>
                            <th>البدلات</th>
                            <th>الخصومات</th>
                            <th>صافي الراتب</th>
                            <th>التوقيع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${payrolls.map(payroll => {
                            const employee = employees.find(emp => emp.id === payroll.employee_id);
                            return `
                                <tr>
                                    <td>${employee?.name || 'غير معروف'}</td>
                                    <td>${employee?.employee_number || ''}</td>
                                    <td>${window.samApp.formatCurrency(payroll.basic_salary || 0)}</td>
                                    <td>${window.samApp.formatCurrency(payroll.total_allowances || 0)}</td>
                                    <td>${window.samApp.formatCurrency(payroll.total_deductions || 0)}</td>
                                    <td class="fw-bold">${window.samApp.formatCurrency(payroll.net_salary || 0)}</td>
                                    <td style="width: 100px;">___________</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                    <tfoot class="table-warning">
                        <tr class="fw-bold">
                            <td colspan="2">الإجمالي</td>
                            <td>${window.samApp.formatCurrency(totals.basicSalary)}</td>
                            <td>${window.samApp.formatCurrency(totals.allowances)}</td>
                            <td>${window.samApp.formatCurrency(totals.deductions)}</td>
                            <td>${window.samApp.formatCurrency(totals.netSalary)}</td>
                            <td>-</td>
                        </tr>
                    </tfoot>
                </table>

                <div class="mt-4 text-center">
                    <p><small>تم إنشاء هذا الإيصال بواسطة نظام SAM لإدارة شؤون الموظفين</small></p>
                </div>
            </div>
        `;
    }

    viewReceipt(receiptId) {
        const receipt = Database.getById('receipts', receiptId);
        if (!receipt) {
            window.samApp.showAlert('الإيصال غير موجود', 'danger');
            return;
        }

        const modal = new bootstrap.Modal(document.getElementById('receiptModal'));
        const content = document.getElementById('receiptContent');

        content.innerHTML = this.generateReceiptHTML(receipt);
        this.currentReceiptId = receiptId;
        modal.show();
    }

    generateReceiptHTML(receipt) {
        const settings = Database.getSettings();
        const employee = Database.getEmployee(receipt.employee_id);

        return `
            <div style="direction: rtl; font-family: Arial, sans-serif;">
                <div class="text-center mb-4">
                    <h3>${settings.company.name}</h3>
                    <p>${settings.company.address}</p>
                    <p>هاتف: ${settings.company.phone}</p>
                    <hr>
                    <h4>إيصال قبض راتب</h4>
                    <p>رقم الإيصال: <strong>${receipt.receipt_number}</strong></p>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>اسم الموظف:</strong> ${employee?.name || 'غير معروف'}</p>
                        <p><strong>الرقم الوظيفي:</strong> ${employee?.employee_number || ''}</p>
                        <p><strong>القسم:</strong> ${employee?.department || 'غير محدد'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الشهر:</strong> ${this.formatMonth(receipt.month)}</p>
                        <p><strong>تاريخ الإصدار:</strong> ${window.samApp.formatDate(receipt.created_at)}</p>
                        <p><strong>الحالة:</strong> ${this.getStatusBadge(receipt.status)}</p>
                    </div>
                </div>

                <table class="table table-bordered">
                    <tr><td><strong>الراتب الأساسي:</strong></td><td>${window.samApp.formatCurrency(receipt.payroll_details?.basic_salary || 0)}</td></tr>
                    <tr><td><strong>إجمالي البدلات:</strong></td><td>${window.samApp.formatCurrency(receipt.payroll_details?.allowances || 0)}</td></tr>
                    <tr><td><strong>إجمالي الاستحقاقات:</strong></td><td>${window.samApp.formatCurrency(receipt.payroll_details?.gross_salary || 0)}</td></tr>
                    <tr><td><strong>إجمالي الخصومات:</strong></td><td>${window.samApp.formatCurrency(receipt.payroll_details?.deductions || 0)}</td></tr>
                    <tr class="table-success"><td><strong>صافي الراتب المستلم:</strong></td><td class="fw-bold">${window.samApp.formatCurrency(receipt.amount)}</td></tr>
                </table>

                <div class="mt-4">
                    <p><strong>أقر أنا الموظف المذكور أعلاه بأنني استلمت راتبي كاملاً للشهر المحدد</strong></p>
                    <br>
                    <div class="row">
                        <div class="col-md-6">
                            <p>توقيع الموظف: _______________</p>
                        </div>
                        <div class="col-md-6">
                            <p>التاريخ: ${window.samApp.formatDate(new Date().toISOString())}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    printReceipt(receiptId) {
        const receipt = Database.getById('receipts', receiptId);
        if (!receipt) {
            window.samApp.showAlert('الإيصال غير موجود', 'danger');
            return;
        }

        const content = this.generateReceiptHTML(receipt);
        const title = `إيصال قبض راتب - ${receipt.receipt_number}`;

        window.printManager.printReport(title, content, {
            showDate: false,
            showCompanyInfo: false
        });
    }

    printCurrentReceipt() {
        if (this.currentReceiptId) {
            this.printReceipt(this.currentReceiptId);
        }
    }

    printCurrentSummaryReceipt() {
        const content = document.getElementById('summaryReceiptContent').innerHTML;
        const title = `الإيصال الإجمالي للرواتب - ${this.formatMonth(this.currentMonth)}`;

        window.printManager.printReport(title, content, {
            showDate: false,
            showCompanyInfo: false
        });
    }

    printAllReceipts() {
        const receipts = Database.getAll('receipts') || [];
        const filteredReceipts = receipts.filter(receipt => {
            let match = true;
            if (this.currentMonth) match = match && receipt.month === this.currentMonth;
            if (this.selectedEmployee) match = match && receipt.employee_id === this.selectedEmployee;
            return match;
        });

        if (filteredReceipts.length === 0) {
            window.samApp.showAlert('لا توجد إيصالات للطباعة', 'warning');
            return;
        }

        filteredReceipts.forEach((receipt, index) => {
            setTimeout(() => {
                this.printReceipt(receipt.id);
            }, index * 1000); // تأخير ثانية واحدة بين كل طباعة
        });
    }

    markAsPaid(receiptId) {
        if (confirm('هل أنت متأكد من تأكيد دفع هذا الإيصال؟')) {
            try {
                Database.update('receipts', receiptId, {
                    status: 'paid',
                    paid_at: new Date().toISOString()
                });
                window.samApp.showAlert('تم تأكيد الدفع بنجاح', 'success');
                this.loadReceiptsData();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    markReceiptAsPaid() {
        if (this.currentReceiptId) {
            this.markAsPaid(this.currentReceiptId);
            const modal = bootstrap.Modal.getInstance(document.getElementById('receiptModal'));
            modal.hide();
        }
    }

    formatMonth(monthStr) {
        const [year, month] = monthStr.split('-');
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[parseInt(month) - 1]} ${year}`;
    }
}

// Global reference
let receiptsManager;
