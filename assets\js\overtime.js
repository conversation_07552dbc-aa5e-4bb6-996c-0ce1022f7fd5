/**
 * SAM - نظام إدارة شؤون الموظفين
 * Overtime and Deductions Calculator
 * حاسبة الساعات الإضافية والخصومات
 */

class OvertimeCalculator {
    constructor() {
        this.settings = Database.getSettings();
    }

    /**
     * حساب الساعات الإضافية للموظف في يوم معين
     */
    calculateDailyOvertime(employeeId, date, checkIn, checkOut) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) return { overtime: 0, undertime: 0 };

        // أوقات العمل المخصصة للموظف أو الافتراضية
        const workStartTime = employee.work_start_time || this.settings.working_hours.start_time;
        const workEndTime = employee.work_end_time || this.settings.working_hours.end_time;
        const breakDuration = employee.break_duration || this.settings.working_hours.break_duration;

        // تحويل الأوقات إلى دقائق
        const workStart = this.timeToMinutes(workStartTime);
        const workEnd = this.timeToMinutes(workEndTime);
        const actualStart = this.timeToMinutes(checkIn);
        const actualEnd = this.timeToMinutes(checkOut);

        // حساب ساعات العمل المطلوبة (بدون فترة الراحة)
        const requiredWorkMinutes = (workEnd - workStart) - breakDuration;
        
        // حساب ساعات العمل الفعلية (بدون فترة الراحة)
        const actualWorkMinutes = (actualEnd - actualStart) - breakDuration;

        // حساب الإضافي والنقص
        const overtime = Math.max(0, actualWorkMinutes - requiredWorkMinutes);
        const undertime = Math.max(0, requiredWorkMinutes - actualWorkMinutes);

        return {
            overtime: Math.round(overtime),
            undertime: Math.round(undertime),
            requiredMinutes: requiredWorkMinutes,
            actualMinutes: actualWorkMinutes
        };
    }

    /**
     * حساب الساعات الإضافية الشهرية للموظف
     */
    calculateMonthlyOvertime(employeeId, month) {
        const attendance = Database.getAttendance({ 
            employee_id: employeeId, 
            month: month 
        });

        let totalOvertime = 0;
        let totalUndertime = 0;
        let overtimeDays = 0;
        let undertimeDays = 0;

        attendance.forEach(record => {
            if (record.check_in && record.check_out && record.status !== 'absent') {
                const dailyCalc = this.calculateDailyOvertime(
                    employeeId, 
                    record.date, 
                    record.check_in, 
                    record.check_out
                );
                
                if (dailyCalc.overtime > 0) {
                    totalOvertime += dailyCalc.overtime;
                    overtimeDays++;
                }
                
                if (dailyCalc.undertime > 0) {
                    totalUndertime += dailyCalc.undertime;
                    undertimeDays++;
                }
            }
        });

        return {
            totalOvertimeMinutes: totalOvertime,
            totalOvertimeHours: Math.round((totalOvertime / 60) * 100) / 100,
            totalUndertimeMinutes: totalUndertime,
            totalUndertimeHours: Math.round((totalUndertime / 60) * 100) / 100,
            overtimeDays: overtimeDays,
            undertimeDays: undertimeDays
        };
    }

    /**
     * حساب قيمة الساعات الإضافية بالمال
     */
    calculateOvertimePay(employeeId, overtimeHours) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) return 0;

        const monthlySalary = employee.salary || 0;
        const workingDaysPerMonth = 22; // متوسط أيام العمل في الشهر
        const hoursPerDay = 8; // ساعات العمل اليومية
        
        // حساب الأجر بالساعة
        const hourlyRate = monthlySalary / (workingDaysPerMonth * hoursPerDay);
        
        // معدل الإضافي (عادة 1.5 من الأجر العادي)
        const overtimeRate = this.settings.attendance?.overtime_rate || 1.5;
        
        return Math.round((hourlyRate * overtimeRate * overtimeHours) * 100) / 100;
    }

    /**
     * حساب خصم النقص في ساعات العمل
     */
    calculateUndertimeDeduction(employeeId, undertimeHours) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) return 0;

        const monthlySalary = employee.salary || 0;
        const workingDaysPerMonth = 22;
        const hoursPerDay = 8;
        
        // حساب الأجر بالساعة
        const hourlyRate = monthlySalary / (workingDaysPerMonth * hoursPerDay);
        
        return Math.round((hourlyRate * undertimeHours) * 100) / 100;
    }

    /**
     * حساب خصم التأخير
     */
    calculateLatenessDeduction(employeeId, month) {
        const attendance = Database.getAttendance({ 
            employee_id: employeeId, 
            month: month 
        });

        const employee = Database.getEmployee(employeeId);
        const lateThreshold = parseInt(employee?.late_tolerance) || 15; // دقائق السماح
        
        let totalLateMinutes = 0;
        let lateDays = 0;

        attendance.forEach(record => {
            if (record.status === 'late' && record.check_in) {
                const workStartTime = employee?.work_start_time || this.settings.working_hours.start_time;
                const expectedStart = this.timeToMinutes(workStartTime);
                const actualStart = this.timeToMinutes(record.check_in);
                
                const lateMinutes = Math.max(0, actualStart - expectedStart - lateThreshold);
                if (lateMinutes > 0) {
                    totalLateMinutes += lateMinutes;
                    lateDays++;
                }
            }
        });

        // حساب قيمة الخصم
        const monthlySalary = employee?.salary || 0;
        const workingDaysPerMonth = 22;
        const minutesPerDay = 8 * 60;
        const minuteRate = monthlySalary / (workingDaysPerMonth * minutesPerDay);
        
        const deduction = Math.round((minuteRate * totalLateMinutes) * 100) / 100;

        return {
            totalLateMinutes: totalLateMinutes,
            lateDays: lateDays,
            deduction: deduction
        };
    }

    /**
     * حساب خصم الغياب
     */
    calculateAbsenceDeduction(employeeId, month) {
        const attendance = Database.getAttendance({ 
            employee_id: employeeId, 
            month: month 
        });

        const employee = Database.getEmployee(employeeId);
        const absentDays = attendance.filter(record => record.status === 'absent').length;
        
        if (absentDays === 0) {
            return { absentDays: 0, deduction: 0 };
        }

        const monthlySalary = employee?.salary || 0;
        const workingDaysPerMonth = 22;
        const dailyRate = monthlySalary / workingDaysPerMonth;
        
        const deduction = Math.round((dailyRate * absentDays) * 100) / 100;

        return {
            absentDays: absentDays,
            deduction: deduction
        };
    }

    /**
     * حساب جميع الإضافات والخصومات للموظف في شهر معين
     */
    calculateAllowancesAndDeductions(employeeId, month) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) return null;

        // حساب الساعات الإضافية
        const overtimeCalc = this.calculateMonthlyOvertime(employeeId, month);
        const overtimePay = this.calculateOvertimePay(employeeId, overtimeCalc.totalOvertimeHours);
        
        // حساب خصم النقص في ساعات العمل
        const undertimeDeduction = this.calculateUndertimeDeduction(employeeId, overtimeCalc.totalUndertimeHours);
        
        // حساب خصم التأخير
        const latenessCalc = this.calculateLatenessDeduction(employeeId, month);
        
        // حساب خصم الغياب
        const absenceCalc = this.calculateAbsenceDeduction(employeeId, month);
        
        // حساب خصم السلف
        const advances = Database.getAll('advances') || [];
        const activeAdvances = advances.filter(adv => 
            adv.employee_id === employeeId && 
            adv.status === 'active' &&
            new Date(adv.start_date) <= new Date(month + '-01')
        );
        const advanceDeduction = activeAdvances.reduce((sum, adv) => sum + (adv.monthly_installment || 0), 0);
        
        // حساب خصم الجزاءات
        const penalties = Database.getAll('penalties') || [];
        const monthPenalties = penalties.filter(pen => 
            pen.employee_id === employeeId && 
            pen.penalty_date.startsWith(month) &&
            pen.affects_salary === 'yes' &&
            pen.status === 'active'
        );
        const penaltyDeduction = monthPenalties.reduce((sum, pen) => sum + (pen.amount || 0), 0);

        // حساب بدل المواصلات والسكن (إن وجد)
        const transportAllowance = employee.transport_allowance || 0;
        const housingAllowance = employee.housing_allowance || 0;
        const otherAllowances = employee.other_allowances || 0;

        return {
            // الإضافات
            allowances: {
                overtime: overtimePay,
                transport: transportAllowance,
                housing: housingAllowance,
                other: otherAllowances,
                total: overtimePay + transportAllowance + housingAllowance + otherAllowances
            },
            
            // الخصومات
            deductions: {
                undertime: undertimeDeduction,
                lateness: latenessCalc.deduction,
                absence: absenceCalc.deduction,
                advances: advanceDeduction,
                penalties: penaltyDeduction,
                total: undertimeDeduction + latenessCalc.deduction + absenceCalc.deduction + advanceDeduction + penaltyDeduction
            },
            
            // تفاصيل إضافية
            details: {
                overtime: {
                    hours: overtimeCalc.totalOvertimeHours,
                    days: overtimeCalc.overtimeDays,
                    amount: overtimePay
                },
                undertime: {
                    hours: overtimeCalc.totalUndertimeHours,
                    days: overtimeCalc.undertimeDays,
                    amount: undertimeDeduction
                },
                lateness: {
                    minutes: latenessCalc.totalLateMinutes,
                    days: latenessCalc.lateDays,
                    amount: latenessCalc.deduction
                },
                absence: {
                    days: absenceCalc.absentDays,
                    amount: absenceCalc.deduction
                },
                advances: {
                    count: activeAdvances.length,
                    amount: advanceDeduction
                },
                penalties: {
                    count: monthPenalties.length,
                    amount: penaltyDeduction
                }
            }
        };
    }

    /**
     * تحويل الوقت من صيغة HH:MM إلى دقائق
     */
    timeToMinutes(timeString) {
        if (!timeString) return 0;
        const [hours, minutes] = timeString.split(':').map(Number);
        return (hours * 60) + minutes;
    }

    /**
     * تحويل الدقائق إلى صيغة HH:MM
     */
    minutesToTime(minutes) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
    }

    /**
     * تحويل الدقائق إلى ساعات عشرية
     */
    minutesToHours(minutes) {
        return Math.round((minutes / 60) * 100) / 100;
    }
}

// إنشاء مثيل عام للاستخدام في جميع أنحاء التطبيق
window.overtimeCalculator = new OvertimeCalculator();
