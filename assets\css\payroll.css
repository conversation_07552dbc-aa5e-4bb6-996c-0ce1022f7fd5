/**
 * SAM - نظام إدارة شؤون الموظفين
 * Payroll Module Styles
 * أنماط وحدة الرواتب
 */

/* تحسينات عامة لصفحة الرواتب */
.payroll-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

/* بطاقات الإحصائيات */
.payroll-stats .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.payroll-stats .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.payroll-stats .card-body {
    padding: 1.5rem;
}

.payroll-stats .card h4 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* جدول الرواتب */
.payroll-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.payroll-table .table {
    margin-bottom: 0;
}

.payroll-table .table thead th {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem 0.75rem;
    text-align: center;
}

.payroll-table .table tbody tr {
    transition: background-color 0.3s ease;
}

.payroll-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

.payroll-table .table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* أزرار الإجراءات */
.btn-group-sm .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.btn-group-sm .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* نموذج الراتب */
.payroll-modal .modal-dialog {
    max-width: 90%;
}

.payroll-modal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.payroll-modal .modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.payroll-modal .modal-title {
    font-weight: 600;
}

.payroll-modal .btn-close {
    filter: invert(1);
}

/* أقسام النموذج */
.payroll-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-right: 4px solid #007bff;
}

.payroll-section h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.payroll-section h6 i {
    margin-left: 0.5rem;
}

/* حقول القراءة فقط */
.form-control[readonly] {
    background-color: #e9ecef;
    border-color: #ced4da;
    font-weight: 600;
    color: #495057;
}

/* مؤشرات الحالة */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-paid {
    background-color: #d1edff;
    color: #0c5460;
    border: 1px solid #b8daff;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* مؤشر التحميل */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .payroll-stats .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .payroll-modal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .payroll-container {
        background: white;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .btn, .btn-group {
        display: none !important;
    }
    
    .table {
        font-size: 12px;
    }
}

/* تأثيرات الانتقال */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات للألوان */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-primary {
    color: #007bff !important;
}

/* تحسينات للمساحات */
.mb-4 {
    margin-bottom: 1.5rem !important;
}

.py-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
}

/* تحسينات للحدود */
.border-radius-lg {
    border-radius: 15px !important;
}

.border-radius-sm {
    border-radius: 6px !important;
}
