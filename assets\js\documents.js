/**
 * SAM - نظام إدارة شؤون الموظفين
 * Documents Management Module
 * وحدة إدارة الوثائق والمستندات
 */

class DocumentsManager {
    constructor() {
        this.selectedDocument = null;
        this.documentTemplates = this.getDocumentTemplates();
    }

    render() {
        if (!window.authManager.hasPermission('employees')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadDocumentsData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-file-alt me-2"></i>
                            إدارة الوثائق والمستندات
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-primary" id="createDocumentBtn">
                                <i class="fas fa-plus me-2"></i>
                                إنشاء وثيقة
                            </button>
                            <button class="btn btn-success" id="uploadDocumentBtn">
                                <i class="fas fa-upload me-2"></i>
                                رفع ملف
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalDocuments">0</h4>
                                    <p class="mb-0">إجمالي الوثائق</p>
                                </div>
                                <i class="fas fa-file-alt fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="templatesCount">0</h4>
                                    <p class="mb-0">القوالب</p>
                                </div>
                                <i class="fas fa-file-contract fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="recentDocuments">0</h4>
                                    <p class="mb-0">وثائق هذا الشهر</p>
                                </div>
                                <i class="fas fa-calendar fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="pendingApproval">0</h4>
                                    <p class="mb-0">في انتظار الموافقة</p>
                                </div>
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <select class="form-select" id="typeFilter">
                        <option value="">جميع الأنواع</option>
                        <option value="leave_request">طلب إجازة</option>
                        <option value="permission_request">طلب إذن</option>
                        <option value="contract">عقد عمل</option>
                        <option value="certificate">شهادة</option>
                        <option value="report">تقرير</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="draft">مسودة</option>
                        <option value="pending">في الانتظار</option>
                        <option value="approved">موافق عليه</option>
                        <option value="rejected">مرفوض</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في الوثائق...">
                </div>
            </div>

            <!-- Documents Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الوثائق
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>عنوان الوثيقة</th>
                                    <th>النوع</th>
                                    <th>الموظف</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="documentsTableBody">
                                <!-- Documents will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Document Modal -->
            <div class="modal fade" id="documentModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="documentModalTitle">إنشاء وثيقة جديدة</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="documentForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">عنوان الوثيقة *</label>
                                        <input type="text" class="form-control" name="title" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع الوثيقة *</label>
                                        <select class="form-select" name="type" required>
                                            <option value="">اختر النوع</option>
                                            <option value="leave_request">طلب إجازة</option>
                                            <option value="permission_request">طلب إذن</option>
                                            <option value="contract">عقد عمل</option>
                                            <option value="certificate">شهادة</option>
                                            <option value="report">تقرير</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموظف المعني</label>
                                        <select class="form-select" name="employee_id">
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">القالب</label>
                                        <select class="form-select" id="templateSelect">
                                            <option value="">اختر قالب (اختياري)</option>
                                        </select>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">محتوى الوثيقة *</label>
                                        <textarea class="form-control" name="content" rows="15" required></textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="draft">مسودة</option>
                                            <option value="pending">في الانتظار</option>
                                            <option value="approved">موافق عليه</option>
                                            <option value="rejected">مرفوض</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الانتهاء</label>
                                        <input type="date" class="form-control" name="expiry_date">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-info" id="previewDocumentBtn">معاينة</button>
                            <button type="button" class="btn btn-primary" id="saveDocumentBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload Modal -->
            <div class="modal fade" id="uploadModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">رفع ملف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="uploadForm">
                                <div class="mb-3">
                                    <label class="form-label">اختر الملف</label>
                                    <input type="file" class="form-control" name="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">عنوان الوثيقة</label>
                                    <input type="text" class="form-control" name="title" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نوع الوثيقة</label>
                                    <select class="form-select" name="type" required>
                                        <option value="">اختر النوع</option>
                                        <option value="contract">عقد عمل</option>
                                        <option value="certificate">شهادة</option>
                                        <option value="id_copy">صورة هوية</option>
                                        <option value="cv">سيرة ذاتية</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الموظف المعني</label>
                                    <select class="form-select" name="employee_id">
                                        <option value="">اختر الموظف</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="uploadFileBtn">رفع</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Preview Modal -->
            <div class="modal fade" id="previewModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">معاينة الوثيقة</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="previewContent">
                            <!-- Preview content will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="printPreviewBtn">طباعة</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getDocumentTemplates() {
        return {
            leave_request: {
                name: 'طلب إجازة',
                content: `بسم الله الرحمن الرحيم

إلى: مدير الموارد البشرية
الموضوع: طلب إجازة

السيد المحترم،

أتقدم إليكم بطلب الحصول على إجازة لمدة [عدد الأيام] أيام، وذلك اعتباراً من تاريخ [تاريخ البداية] إلى تاريخ [تاريخ النهاية].

سبب الإجازة: [سبب الإجازة]

أرجو الموافقة على طلبي، وتفضلوا بقبول فائق الاحترام.

مقدم الطلب: [اسم الموظف]
الرقم الوظيفي: [الرقم الوظيفي]
القسم: [القسم]
التاريخ: [التاريخ]
التوقيع: _______________`
            },
            permission_request: {
                name: 'طلب إذن',
                content: `بسم الله الرحمن الرحيم

إلى: المدير المباشر
الموضوع: طلب إذن

السيد المحترم،

أتقدم إليكم بطلب الحصول على إذن [نوع الإذن] وذلك في تاريخ [التاريخ] من الساعة [وقت البداية] إلى الساعة [وقت النهاية].

سبب الإذن: [سبب الإذن]

أتعهد بتعويض الوقت المفقود حسب اللوائح المعمول بها.

أرجو الموافقة على طلبي، وتفضلوا بقبول فائق الاحترام.

مقدم الطلب: [اسم الموظف]
الرقم الوظيفي: [الرقم الوظيفي]
القسم: [القسم]
التاريخ: [التاريخ]
التوقيع: _______________`
            },
            certificate: {
                name: 'شهادة عمل',
                content: `بسم الله الرحمن الرحيم

شهادة عمل

نشهد نحن [اسم الشركة] بأن السيد/ة [اسم الموظف] يعمل لدينا في منصب [المنصب] بقسم [القسم] منذ تاريخ [تاريخ التوظيف] وحتى تاريخه.

وقد أبدى/ت خلال فترة عمله/ا التزاماً وجدية في العمل، وحسن سلوك وأخلاق عالية.

راتبه/ا الشهري: [الراتب]

وقد أعطيت له/ا هذه الشهادة بناءً على طلبه/ا دون أدنى مسؤولية علينا.

والله الموفق،،،

[اسم الشركة]
إدارة الموارد البشرية
التاريخ: [التاريخ]
الختم: _______________`
            },
            contract: {
                name: 'عقد عمل',
                content: `بسم الله الرحمن الرحيم

عقد عمل

الطرف الأول: [اسم الشركة]
العنوان: [عنوان الشركة]
هاتف: [هاتف الشركة]

الطرف الثاني: [اسم الموظف]
رقم الهوية: [رقم الهوية]
العنوان: [عنوان الموظف]
هاتف: [هاتف الموظف]

بناءً على ما تقدم، فقد اتفق الطرفان على ما يلي:

المادة الأولى: طبيعة العمل
يتعهد الطرف الثاني بالعمل لدى الطرف الأول في منصب [المنصب] بقسم [القسم].

المادة الثانية: مدة العقد
مدة هذا العقد [مدة العقد] تبدأ من تاريخ [تاريخ البداية] وتنتهي في [تاريخ النهاية].

المادة الثالثة: الراتب والمزايا
يتقاضى الطرف الثاني راتباً شهرياً قدره [الراتب] بالإضافة إلى المزايا المقررة.

المادة الرابعة: ساعات العمل
ساعات العمل من [وقت البداية] إلى [وقت النهاية] من [أيام العمل].

وقد وقع هذا العقد من نسختين بيد كل طرف نسخة للعمل بموجبها.

الطرف الأول: _______________
الطرف الثاني: _______________
التاريخ: [التاريخ]`
            }
        };
    }

    bindEvents() {
        // Create document button
        document.getElementById('createDocumentBtn').addEventListener('click', () => {
            this.showDocumentModal();
        });

        // Upload document button
        document.getElementById('uploadDocumentBtn').addEventListener('click', () => {
            this.showUploadModal();
        });

        // Save document
        document.getElementById('saveDocumentBtn').addEventListener('click', () => {
            this.saveDocument();
        });

        // Preview document
        document.getElementById('previewDocumentBtn').addEventListener('click', () => {
            this.previewDocument();
        });

        // Upload file
        document.getElementById('uploadFileBtn').addEventListener('click', () => {
            this.uploadFile();
        });

        // Print preview
        document.getElementById('printPreviewBtn').addEventListener('click', () => {
            this.printPreview();
        });

        // Template selection
        document.getElementById('templateSelect').addEventListener('change', (e) => {
            this.loadTemplate(e.target.value);
        });

        // Filters
        document.getElementById('typeFilter').addEventListener('change', () => {
            this.loadDocumentsTable();
        });

        document.getElementById('statusFilter').addEventListener('change', () => {
            this.loadDocumentsTable();
        });

        document.getElementById('employeeFilter').addEventListener('change', () => {
            this.loadDocumentsTable();
        });

        document.getElementById('searchInput').addEventListener('input', () => {
            this.loadDocumentsTable();
        });
    }

    loadDocumentsData() {
        this.loadEmployeeOptions();
        this.loadTemplateOptions();
        this.loadDocumentsTable();
        this.updateDocumentStats();
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const selects = document.querySelectorAll('#employeeFilter, select[name="employee_id"]');

        selects.forEach(select => {
            const isFilter = select.id === 'employeeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الموظفين</option>' : '<option value="">اختر الموظف</option>';

            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadTemplateOptions() {
        const select = document.getElementById('templateSelect');
        select.innerHTML = '<option value="">اختر قالب (اختياري)</option>';

        Object.keys(this.documentTemplates).forEach(key => {
            const template = this.documentTemplates[key];
            select.innerHTML += `<option value="${key}">${template.name}</option>`;
        });
    }

    loadTemplate(templateKey) {
        if (!templateKey) return;

        const template = this.documentTemplates[templateKey];
        if (template) {
            document.querySelector('textarea[name="content"]').value = template.content;
        }
    }

    loadDocumentsTable() {
        let documents = Database.getAll('documents') || [];

        // Apply filters
        const typeFilter = document.getElementById('typeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const employeeFilter = document.getElementById('employeeFilter').value;
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();

        if (typeFilter) {
            documents = documents.filter(doc => doc.type === typeFilter);
        }

        if (statusFilter) {
            documents = documents.filter(doc => doc.status === statusFilter);
        }

        if (employeeFilter) {
            documents = documents.filter(doc => doc.employee_id === employeeFilter);
        }

        if (searchTerm) {
            documents = documents.filter(doc =>
                doc.title.toLowerCase().includes(searchTerm) ||
                (doc.content && doc.content.toLowerCase().includes(searchTerm))
            );
        }

        // Sort by creation date (newest first)
        documents.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        this.renderDocumentsTable(documents);
    }

    renderDocumentsTable(documents) {
        const tbody = document.getElementById('documentsTableBody');
        const employees = Database.getEmployees();

        if (documents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4">
                        <i class="fas fa-file-alt fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد وثائق</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = documents.map(doc => {
            const employee = employees.find(emp => emp.id === doc.employee_id);
            const statusBadge = this.getStatusBadge(doc.status);
            const typeBadge = this.getTypeBadge(doc.type);

            return `
                <tr>
                    <td>
                        <div class="fw-bold">${doc.title}</div>
                        <small class="text-muted">${doc.notes || ''}</small>
                    </td>
                    <td>${typeBadge}</td>
                    <td>${employee ? employee.name : 'غير محدد'}</td>
                    <td>${window.samApp.formatDate(doc.created_at)}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary view-document"
                                    data-document-id="${doc.id}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success edit-document"
                                    data-document-id="${doc.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info print-document"
                                    data-document-id="${doc.id}">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-outline-danger delete-document"
                                    data-document-id="${doc.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View document
        document.querySelectorAll('.view-document').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const docId = e.target.closest('.view-document').dataset.documentId;
                this.viewDocument(docId);
            });
        });

        // Edit document
        document.querySelectorAll('.edit-document').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const docId = e.target.closest('.edit-document').dataset.documentId;
                const document = Database.getById('documents', docId);
                this.showDocumentModal(document);
            });
        });

        // Print document
        document.querySelectorAll('.print-document').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const docId = e.target.closest('.print-document').dataset.documentId;
                const document = Database.getById('documents', docId);
                this.printDocument(document);
            });
        });

        // Delete document
        document.querySelectorAll('.delete-document').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const docId = e.target.closest('.delete-document').dataset.documentId;
                this.deleteDocument(docId);
            });
        });
    }

    getStatusBadge(status) {
        const statusMap = {
            'draft': { class: 'secondary', text: 'مسودة' },
            'pending': { class: 'warning', text: 'في الانتظار' },
            'approved': { class: 'success', text: 'موافق عليه' },
            'rejected': { class: 'danger', text: 'مرفوض' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    getTypeBadge(type) {
        const typeMap = {
            'leave_request': { class: 'primary', text: 'طلب إجازة' },
            'permission_request': { class: 'info', text: 'طلب إذن' },
            'contract': { class: 'success', text: 'عقد عمل' },
            'certificate': { class: 'warning', text: 'شهادة' },
            'report': { class: 'dark', text: 'تقرير' },
            'other': { class: 'secondary', text: 'أخرى' }
        };

        const typeInfo = typeMap[type] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${typeInfo.class}">${typeInfo.text}</span>`;
    }

    updateDocumentStats() {
        const documents = Database.getAll('documents') || [];
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        const totalDocuments = documents.length;
        const templatesCount = Object.keys(this.documentTemplates).length;
        const recentDocuments = documents.filter(doc => {
            const docDate = new Date(doc.created_at);
            return docDate.getMonth() === currentMonth && docDate.getFullYear() === currentYear;
        }).length;
        const pendingApproval = documents.filter(doc => doc.status === 'pending').length;

        document.getElementById('totalDocuments').textContent = totalDocuments;
        document.getElementById('templatesCount').textContent = templatesCount;
        document.getElementById('recentDocuments').textContent = recentDocuments;
        document.getElementById('pendingApproval').textContent = pendingApproval;
    }

    showDocumentModal(documentData = null) {
        const modal = new bootstrap.Modal(document.getElementById('documentModal'));
        const form = document.getElementById('documentForm');
        const title = document.getElementById('documentModalTitle');

        if (documentData) {
            title.textContent = 'تعديل الوثيقة';
            this.populateDocumentForm(form, documentData);
            this.selectedDocument = documentData;
        } else {
            title.textContent = 'إنشاء وثيقة جديدة';
            form.reset();
            form.querySelector('select[name="status"]').value = 'draft';
            this.selectedDocument = null;
        }

        modal.show();
    }

    showUploadModal() {
        const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
        const form = document.getElementById('uploadForm');
        form.reset();
        modal.show();
    }

    populateDocumentForm(form, documentData) {
        Object.keys(documentData).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = documentData[key] || '';
            }
        });
    }

    saveDocument() {
        const form = document.getElementById('documentForm');
        const formData = new FormData(form);
        const documentData = Object.fromEntries(formData.entries());

        try {
            if (this.selectedDocument) {
                // Update existing document
                Database.update('documents', this.selectedDocument.id, documentData);
                window.samApp.showAlert('تم تحديث الوثيقة بنجاح', 'success');
            } else {
                // Create new document
                Database.create('documents', documentData);
                window.samApp.showAlert('تم إنشاء الوثيقة بنجاح', 'success');
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('documentModal'));
            modal.hide();
            this.loadDocumentsData();

        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    previewDocument() {
        const form = document.getElementById('documentForm');
        const formData = new FormData(form);
        const documentData = Object.fromEntries(formData.entries());

        if (!documentData.content) {
            window.samApp.showAlert('يرجى إدخال محتوى الوثيقة', 'warning');
            return;
        }

        this.showPreview(documentData);
    }

    showPreview(documentData) {
        const modal = new bootstrap.Modal(document.getElementById('previewModal'));
        const content = document.getElementById('previewContent');
        const settings = Database.getSettings();

        // Replace placeholders with actual data
        let processedContent = this.processDocumentContent(documentData);

        // Enhanced preview with better styling and layout
        content.innerHTML = `
            <div style="direction: rtl; font-family: 'Segoe UI', Tahoma, Arial, sans-serif; line-height: 1.8; padding: 30px; background: white; max-width: 800px; margin: 0 auto; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                <!-- Company Header -->
                <div style="text-align: center; margin-bottom: 40px; border-bottom: 2px solid #007bff; padding-bottom: 20px;">
                    <h1 style="color: #007bff; margin-bottom: 10px; font-size: 24px;">${settings.company.name}</h1>
                    <p style="margin: 5px 0; color: #666; font-size: 14px;">${settings.company.address}</p>
                    <p style="margin: 5px 0; color: #666; font-size: 14px;">
                        هاتف: ${settings.company.phone} |
                        بريد إلكتروني: ${settings.company.email}
                        ${settings.company.website ? ` | الموقع: ${settings.company.website}` : ''}
                    </p>
                    ${settings.company.registration_number ? `<p style="margin: 5px 0; color: #666; font-size: 12px;">رقم التسجيل: ${settings.company.registration_number}</p>` : ''}
                </div>

                <!-- Document Title -->
                <div style="text-align: center; margin-bottom: 30px;">
                    <h2 style="color: #333; margin-bottom: 10px; font-size: 20px; text-decoration: underline;">${documentData.title || 'وثيقة'}</h2>
                    <p style="color: #666; font-size: 14px;">التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                <!-- Document Content -->
                <div style="white-space: pre-line; font-size: 16px; line-height: 2; color: #333; text-align: justify; margin-bottom: 40px;">
                    ${processedContent}
                </div>

                <!-- Document Footer -->
                <div style="margin-top: 50px; border-top: 1px solid #ddd; padding-top: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #666;">
                        <div>
                            <p style="margin: 0;">تم إنشاء هذه الوثيقة بواسطة نظام SAM</p>
                            <p style="margin: 0;">تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</p>
                        </div>
                        <div style="text-align: left;">
                            <p style="margin: 0;">نوع الوثيقة: ${this.getDocumentTypeText(documentData.type)}</p>
                            <p style="margin: 0;">الحالة: ${this.getStatusText(documentData.status)}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Store current document data for printing
        this.currentPreviewData = documentData;
        modal.show();
    }

    getDocumentTypeText(type) {
        const typeMap = {
            'leave_request': 'طلب إجازة',
            'permission_request': 'طلب إذن',
            'work_certificate': 'شهادة عمل',
            'employment_contract': 'عقد عمل',
            'salary_certificate': 'شهادة راتب',
            'experience_certificate': 'شهادة خبرة',
            'custom': 'وثيقة مخصصة',
            'other': 'أخرى'
        };
        return typeMap[type] || 'غير محدد';
    }

    getStatusText(status) {
        const statusMap = {
            'draft': 'مسودة',
            'pending': 'في الانتظار',
            'approved': 'موافق عليه',
            'rejected': 'مرفوض'
        };
        return statusMap[status] || 'غير محدد';
    }

    processDocumentContent(documentData) {
        let content = documentData.content;
        const employee = documentData.employee_id ? Database.getEmployee(documentData.employee_id) : null;
        const settings = Database.getSettings();
        const departments = Database.getAll('departments');
        const positions = Database.getAll('positions');

        // Get department and position names
        const department = employee && departments.find(d => d.id === employee.department);
        const position = employee && positions.find(p => p.id === employee.position);

        // Enhanced replacements with more details
        const replacements = {
            // Company information
            '[اسم الشركة]': settings.company.name || 'اسم الشركة',
            '[عنوان الشركة]': settings.company.address || 'عنوان الشركة',
            '[هاتف الشركة]': settings.company.phone || 'هاتف الشركة',
            '[بريد الشركة]': settings.company.email || 'بريد الشركة الإلكتروني',
            '[موقع الشركة]': settings.company.website || 'موقع الشركة',
            '[رقم تسجيل الشركة]': settings.company.registration_number || 'رقم تسجيل الشركة',

            // Date information
            '[التاريخ]': new Date().toLocaleDateString('ar-SA'),
            '[التاريخ الهجري]': this.getHijriDate(),
            '[الوقت]': new Date().toLocaleTimeString('ar-SA'),
            '[اليوم]': this.getDayName(),
            '[الشهر]': this.getMonthName(),
            '[السنة]': new Date().getFullYear().toString(),

            // Employee information
            '[اسم الموظف]': employee?.name || '[اسم الموظف غير محدد]',
            '[الرقم الوظيفي]': employee?.employee_number || '[الرقم الوظيفي غير محدد]',
            '[القسم]': department?.name || '[القسم غير محدد]',
            '[المنصب]': position?.name || '[المنصب غير محدد]',
            '[الراتب]': employee ? window.samApp.formatCurrency(employee.salary || 0) : '[الراتب غير محدد]',
            '[الراتب بالأرقام]': employee?.salary?.toString() || '[0]',
            '[الراتب بالكلمات]': employee ? this.numberToWords(employee.salary || 0) : '[صفر]',
            '[تاريخ التوظيف]': employee?.hire_date ? window.samApp.formatDate(employee.hire_date) : '[تاريخ التوظيف غير محدد]',
            '[عنوان الموظف]': employee?.address || '[عنوان الموظف غير محدد]',
            '[هاتف الموظف]': employee?.phone || '[هاتف الموظف غير محدد]',
            '[بريد الموظف]': employee?.email || '[بريد الموظف غير محدد]',
            '[رقم الهوية]': employee?.national_id || '[رقم الهوية غير محدد]',
            '[تاريخ الميلاد]': employee?.birth_date ? window.samApp.formatDate(employee.birth_date) : '[تاريخ الميلاد غير محدد]',
            '[الجنس]': employee?.gender === 'male' ? 'ذكر' : employee?.gender === 'female' ? 'أنثى' : '[الجنس غير محدد]',
            '[حالة الموظف]': this.getEmployeeStatusText(employee?.status),

            // Work schedule information
            '[وقت بداية العمل]': employee?.work_start_time || settings.working_hours?.start_time || '08:00',
            '[وقت نهاية العمل]': employee?.work_end_time || settings.working_hours?.end_time || '17:00',
            '[مدة الراحة]': (employee?.break_duration || settings.working_hours?.break_duration || 60) + ' دقيقة',
            '[فترة السماح]': (employee?.late_tolerance || 15) + ' دقيقة',

            // Currency
            '[العملة]': settings.company?.currency || 'SAR',
            '[رمز العملة]': settings.company?.currency_symbol || 'ر.س'
        };

        // Replace all placeholders
        Object.keys(replacements).forEach(placeholder => {
            const regex = new RegExp(placeholder.replace(/[[\]]/g, '\\$&'), 'g');
            content = content.replace(regex, replacements[placeholder]);
        });

        return content;
    }

    getHijriDate() {
        try {
            return new Intl.DateTimeFormat('ar-SA-u-ca-islamic', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }).format(new Date());
        } catch {
            return new Date().toLocaleDateString('ar-SA');
        }
    }

    getDayName() {
        const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        return days[new Date().getDay()];
    }

    getMonthName() {
        const months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return months[new Date().getMonth()];
    }

    getEmployeeStatusText(status) {
        const statusMap = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'terminated': 'مفصول',
            'suspended': 'موقوف'
        };
        return statusMap[status] || '[حالة غير محددة]';
    }

    numberToWords(num) {
        if (num === 0) return 'صفر';

        const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
        const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
        const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];

        if (num < 10) return ones[num];
        if (num < 20) return teens[num - 10];
        if (num < 100) {
            const ten = Math.floor(num / 10);
            const one = num % 10;
            return tens[ten] + (one ? ' و' + ones[one] : '');
        }

        // For larger numbers, return the number itself
        return num.toString();
    }

    printPreview() {
        if (!this.currentPreviewData) {
            window.samApp.showAlert('لا توجد وثيقة للطباعة', 'warning');
            return;
        }

        const settings = Database.getSettings();
        const processedContent = this.processDocumentContent(this.currentPreviewData);

        const printContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <title>${this.currentPreviewData.title || 'وثيقة'} - ${settings.company.name}</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    * {
                        box-sizing: border-box;
                        margin: 0;
                        padding: 0;
                    }

                    body {
                        font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                        line-height: 1.8;
                        color: #333;
                        background: white;
                        direction: rtl;
                    }

                    .document-container {
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 30px;
                    }

                    .company-header {
                        text-align: center;
                        margin-bottom: 40px;
                        border-bottom: 2px solid #007bff;
                        padding-bottom: 20px;
                    }

                    .company-name {
                        color: #007bff;
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 10px;
                    }

                    .company-info {
                        color: #666;
                        font-size: 14px;
                        margin: 5px 0;
                    }

                    .document-title {
                        text-align: center;
                        margin-bottom: 30px;
                    }

                    .document-title h2 {
                        color: #333;
                        font-size: 20px;
                        text-decoration: underline;
                        margin-bottom: 10px;
                    }

                    .document-content {
                        white-space: pre-line;
                        font-size: 16px;
                        line-height: 2;
                        text-align: justify;
                        margin-bottom: 40px;
                        min-height: 300px;
                    }

                    .document-footer {
                        margin-top: 50px;
                        border-top: 1px solid #ddd;
                        padding-top: 20px;
                        font-size: 12px;
                        color: #666;
                    }

                    .footer-info {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }

                    .signature-section {
                        margin-top: 60px;
                        display: flex;
                        justify-content: space-between;
                    }

                    .signature-box {
                        text-align: center;
                        width: 200px;
                    }

                    .signature-line {
                        border-bottom: 1px solid #333;
                        height: 50px;
                        margin-bottom: 10px;
                    }

                    @media print {
                        body {
                            margin: 0;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        @page {
                            margin: 1.5cm;
                            size: A4;
                        }

                        .document-container {
                            padding: 0;
                        }

                        .no-print {
                            display: none !important;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="document-container">
                    <!-- Company Header -->
                    <div class="company-header">
                        <div class="company-name">${settings.company.name}</div>
                        <div class="company-info">${settings.company.address}</div>
                        <div class="company-info">
                            هاتف: ${settings.company.phone} |
                            بريد إلكتروني: ${settings.company.email}
                            ${settings.company.website ? ` | الموقع: ${settings.company.website}` : ''}
                        </div>
                        ${settings.company.registration_number ? `<div class="company-info">رقم التسجيل: ${settings.company.registration_number}</div>` : ''}
                    </div>

                    <!-- Document Title -->
                    <div class="document-title">
                        <h2>${this.currentPreviewData.title || 'وثيقة'}</h2>
                        <div class="company-info">التاريخ: ${new Date().toLocaleDateString('ar-SA')}</div>
                    </div>

                    <!-- Document Content -->
                    <div class="document-content">
                        ${processedContent}
                    </div>

                    <!-- Signature Section -->
                    <div class="signature-section">
                        <div class="signature-box">
                            <div class="signature-line"></div>
                            <div>توقيع المسؤول</div>
                        </div>
                        <div class="signature-box">
                            <div class="signature-line"></div>
                            <div>ختم الشركة</div>
                        </div>
                    </div>

                    <!-- Document Footer -->
                    <div class="document-footer">
                        <div class="footer-info">
                            <div>
                                <div>تم إنشاء هذه الوثيقة بواسطة نظام SAM لإدارة شؤون الموظفين</div>
                                <div>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</div>
                            </div>
                            <div style="text-align: left;">
                                <div>نوع الوثيقة: ${this.getDocumentTypeText(this.currentPreviewData.type)}</div>
                                <div>رقم الوثيقة: ${this.currentPreviewData.id || 'غير محدد'}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `;

        const printWindow = window.open('', '_blank', 'width=800,height=600');
        printWindow.document.write(printContent);
        printWindow.document.close();
    }

    uploadFile() {
        const form = document.getElementById('uploadForm');
        const formData = new FormData(form);
        const fileInput = form.querySelector('input[type="file"]');

        if (!fileInput.files[0]) {
            window.samApp.showAlert('يرجى اختيار ملف', 'warning');
            return;
        }

        const file = fileInput.files[0];
        const reader = new FileReader();

        reader.onload = (e) => {
            const documentData = {
                title: formData.get('title'),
                type: formData.get('type'),
                employee_id: formData.get('employee_id'),
                content: `ملف مرفوع: ${file.name}`,
                file_data: e.target.result,
                file_name: file.name,
                file_type: file.type,
                file_size: file.size,
                status: 'draft'
            };

            try {
                Database.create('documents', documentData);
                window.samApp.showAlert('تم رفع الملف بنجاح', 'success');

                const modal = bootstrap.Modal.getInstance(document.getElementById('uploadModal'));
                modal.hide();
                this.loadDocumentsData();

            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        };

        reader.readAsDataURL(file);
    }

    viewDocument(documentId) {
        const document = Database.getById('documents', documentId);
        if (!document) {
            window.samApp.showAlert('الوثيقة غير موجودة', 'danger');
            return;
        }

        this.showPreview(document);
    }

    printDocument(document) {
        const processedContent = this.processDocumentContent(document);
        const settings = Database.getSettings();

        const printContent = `
            <div style="direction: rtl; font-family: Arial, sans-serif; line-height: 1.6; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h2>${settings.company.name}</h2>
                    <p>${settings.company.address}</p>
                    <p>هاتف: ${settings.company.phone} | بريد إلكتروني: ${settings.company.email}</p>
                    <hr>
                </div>
                <div style="white-space: pre-line; font-size: 14px;">
                    ${processedContent}
                </div>
                <div style="margin-top: 40px; text-align: center; font-size: 12px; color: #666;">
                    <p>تم إنشاء هذه الوثيقة بواسطة نظام SAM لإدارة شؤون الموظفين</p>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>
            </div>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${document.title}</title>
                <meta charset="utf-8">
                <style>
                    @media print {
                        body { margin: 0; }
                        @page { margin: 1cm; }
                    }
                </style>
            </head>
            <body>
                ${printContent}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    deleteDocument(documentId) {
        const document = Database.getById('documents', documentId);

        if (confirm(`هل أنت متأكد من حذف الوثيقة "${document?.title}"؟`)) {
            try {
                Database.delete('documents', documentId);
                window.samApp.showAlert('تم حذف الوثيقة بنجاح', 'success');
                this.loadDocumentsData();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }
}

// Global reference
let documentsManager;
