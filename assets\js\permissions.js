/**
 * SAM - نظام إدارة شؤون الموظفين
 * Permissions Management Module
 * وحدة إدارة الأذونات
 */

class PermissionsManager {
    constructor() {
        this.currentView = 'all';
        this.selectedEmployee = '';
        this.selectedStatus = '';
    }

    render() {
        if (!window.authManager.hasPermission('employees')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadPermissionsData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-user-clock me-2"></i>
                            إدارة الأذونات
                        </h2>
                        <button class="btn btn-primary" id="addPermissionBtn">
                            <i class="fas fa-plus me-2"></i>
                            إضافة إذن جديد
                        </button>
                    </div>
                </div>
            </div>

            <!-- Permission Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="pendingCount">0</h4>
                                    <p class="mb-0">في الانتظار</p>
                                </div>
                                <i class="fas fa-hourglass-half fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="approvedCount">0</h4>
                                    <p class="mb-0">موافق عليها</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="rejectedCount">0</h4>
                                    <p class="mb-0">مرفوضة</p>
                                </div>
                                <i class="fas fa-times-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalHours">0</h4>
                                    <p class="mb-0">إجمالي الساعات</p>
                                </div>
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="pending">في الانتظار</option>
                        <option value="approved">موافق عليها</option>
                        <option value="rejected">مرفوضة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="typeFilter">
                        <option value="">جميع الأنواع</option>
                        <option value="early_leave">خروج مبكر</option>
                        <option value="late_arrival">دخول متأخر</option>
                        <option value="break_extension">تمديد استراحة</option>
                        <option value="personal">شخصي</option>
                        <option value="medical">طبي</option>
                        <option value="emergency">طارئ</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-success w-100" id="exportPermissionsBtn">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <!-- Permissions Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        طلبات الأذونات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإذن</th>
                                    <th>التاريخ</th>
                                    <th>من الساعة</th>
                                    <th>إلى الساعة</th>
                                    <th>المدة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="permissionsTableBody">
                                <!-- Permission records will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Permission Modal -->
            <div class="modal fade" id="permissionModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="permissionModalTitle">إضافة إذن جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="permissionForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع الإذن *</label>
                                        <select class="form-select" name="permission_type" required>
                                            <option value="">اختر نوع الإذن</option>
                                            <option value="early_leave">خروج مبكر</option>
                                            <option value="late_arrival">دخول متأخر</option>
                                            <option value="break_extension">تمديد استراحة</option>
                                            <option value="personal">شخصي</option>
                                            <option value="medical">طبي</option>
                                            <option value="emergency">طارئ</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">التاريخ *</label>
                                        <input type="date" class="form-control" name="permission_date" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="pending">في الانتظار</option>
                                            <option value="approved">موافق عليها</option>
                                            <option value="rejected">مرفوضة</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">من الساعة</label>
                                        <input type="time" class="form-control" name="start_time">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">إلى الساعة</label>
                                        <input type="time" class="form-control" name="end_time">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المدة (ساعات)</label>
                                        <input type="number" class="form-control" name="duration" step="0.5" min="0" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">هل يؤثر على الراتب؟</label>
                                        <select class="form-select" name="affects_salary">
                                            <option value="no">لا</option>
                                            <option value="yes">نعم</option>
                                        </select>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">سبب الإذن *</label>
                                        <textarea class="form-control" name="reason" rows="3" required></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات الإدارة</label>
                                        <textarea class="form-control" name="admin_notes" rows="2"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="savePermissionBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Permission Details Modal -->
            <div class="modal fade" id="permissionDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل طلب الإذن</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="permissionDetailsContent">
                            <!-- Permission details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <div id="permissionActions">
                                <!-- Action buttons will be added here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Add permission button
        document.getElementById('addPermissionBtn').addEventListener('click', () => {
            this.showPermissionModal();
        });

        // Filter events
        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadPermissionsData();
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.selectedStatus = e.target.value;
            this.loadPermissionsData();
        });

        // Export button
        document.getElementById('exportPermissionsBtn').addEventListener('click', () => {
            this.exportPermissions();
        });

        // Save permission
        document.getElementById('savePermissionBtn').addEventListener('click', () => {
            this.savePermission();
        });

        // Time change events for calculating duration
        const startTimeInput = document.querySelector('input[name="start_time"]');
        const endTimeInput = document.querySelector('input[name="end_time"]');
        
        if (startTimeInput && endTimeInput) {
            [startTimeInput, endTimeInput].forEach(input => {
                input.addEventListener('change', () => {
                    this.calculateDuration();
                });
            });
        }
    }

    loadPermissionsData() {
        this.loadEmployeeOptions();
        this.loadPermissionsTable();
        this.updatePermissionStats();
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const selects = document.querySelectorAll('#employeeFilter, select[name="employee_id"]');
        
        selects.forEach(select => {
            const isFilter = select.id === 'employeeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الموظفين</option>' : '<option value="">اختر الموظف</option>';
            
            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadPermissionsTable() {
        let permissions = Database.getAll('permissions') || [];
        
        // Apply filters
        if (this.selectedEmployee) {
            permissions = permissions.filter(perm => perm.employee_id === this.selectedEmployee);
        }
        
        if (this.selectedStatus) {
            permissions = permissions.filter(perm => perm.status === this.selectedStatus);
        }
        
        // Sort by creation date (newest first)
        permissions.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        
        this.renderPermissionsTable(permissions);
    }

    renderPermissionsTable(permissions) {
        const tbody = document.getElementById('permissionsTableBody');
        const employees = Database.getEmployees();

        if (permissions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="fas fa-user-clock fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد طلبات أذونات</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = permissions.map(permission => {
            const employee = employees.find(emp => emp.id === permission.employee_id);
            const statusBadge = this.getStatusBadge(permission.status);
            const duration = this.calculatePermissionDuration(permission.start_time, permission.end_time);

            return `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${employee?.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}"
                                 alt="${employee?.name}" class="rounded-circle me-2" width="40" height="40">
                            <div>
                                <div class="fw-bold">${employee?.name || 'غير معروف'}</div>
                                <small class="text-muted">${employee?.employee_number || ''}</small>
                            </div>
                        </div>
                    </td>
                    <td>${this.getPermissionTypeText(permission.permission_type)}</td>
                    <td>${window.samApp.formatDate(permission.permission_date)}</td>
                    <td>${permission.start_time || '-'}</td>
                    <td>${permission.end_time || '-'}</td>
                    <td><span class="badge bg-info">${duration} ساعة</span></td>
                    <td>${statusBadge}</td>
                    <td>${window.samApp.formatDate(permission.created_at)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary view-permission"
                                    data-permission-id="${permission.id}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success edit-permission"
                                    data-permission-id="${permission.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger delete-permission"
                                    data-permission-id="${permission.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View permission
        document.querySelectorAll('.view-permission').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const permissionId = e.target.closest('.view-permission').dataset.permissionId;
                this.viewPermission(permissionId);
            });
        });

        // Edit permission
        document.querySelectorAll('.edit-permission').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const permissionId = e.target.closest('.edit-permission').dataset.permissionId;
                const permission = Database.getById('permissions', permissionId);
                this.showPermissionModal(permission);
            });
        });

        // Delete permission
        document.querySelectorAll('.delete-permission').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const permissionId = e.target.closest('.delete-permission').dataset.permissionId;
                this.deletePermission(permissionId);
            });
        });
    }

    getStatusBadge(status) {
        const statusMap = {
            'pending': { class: 'warning', text: 'في الانتظار' },
            'approved': { class: 'success', text: 'موافق عليها' },
            'rejected': { class: 'danger', text: 'مرفوضة' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    getPermissionTypeText(type) {
        const typeMap = {
            'early_leave': 'خروج مبكر',
            'late_arrival': 'دخول متأخر',
            'break_extension': 'تمديد استراحة',
            'personal': 'شخصي',
            'medical': 'طبي',
            'emergency': 'طارئ'
        };
        return typeMap[type] || 'غير محدد';
    }

    calculatePermissionDuration(startTime, endTime) {
        if (!startTime || !endTime) return '0';

        const start = new Date(`2000-01-01T${startTime}`);
        const end = new Date(`2000-01-01T${endTime}`);
        const diffMs = end - start;
        const diffHours = diffMs / (1000 * 60 * 60);

        return diffHours.toFixed(1);
    }

    updatePermissionStats() {
        const permissions = Database.getAll('permissions') || [];

        const pendingCount = permissions.filter(p => p.status === 'pending').length;
        const approvedCount = permissions.filter(p => p.status === 'approved').length;
        const rejectedCount = permissions.filter(p => p.status === 'rejected').length;
        const totalHours = permissions.reduce((sum, p) => {
            return sum + parseFloat(this.calculatePermissionDuration(p.start_time, p.end_time));
        }, 0);

        document.getElementById('pendingCount').textContent = pendingCount;
        document.getElementById('approvedCount').textContent = approvedCount;
        document.getElementById('rejectedCount').textContent = rejectedCount;
        document.getElementById('totalHours').textContent = totalHours.toFixed(1);
    }

    showPermissionModal(permission = null) {
        const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
        const form = document.getElementById('permissionForm');
        const title = document.getElementById('permissionModalTitle');

        if (permission) {
            title.textContent = 'تعديل طلب الإذن';
            this.populatePermissionForm(form, permission);
            this.selectedPermission = permission;
        } else {
            title.textContent = 'إضافة إذن جديد';
            form.reset();
            form.querySelector('input[name="permission_date"]').value = new Date().toISOString().split('T')[0];
            form.querySelector('select[name="status"]').value = 'pending';
            this.selectedPermission = null;
        }

        modal.show();
    }

    populatePermissionForm(form, permission) {
        Object.keys(permission).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = permission[key] || '';
            }
        });

        this.calculateDuration();
    }

    calculateDuration() {
        const startTime = document.querySelector('input[name="start_time"]').value;
        const endTime = document.querySelector('input[name="end_time"]').value;

        if (startTime && endTime) {
            const duration = this.calculatePermissionDuration(startTime, endTime);
            document.querySelector('input[name="duration"]').value = duration;
        }
    }

    savePermission() {
        const form = document.getElementById('permissionForm');
        const formData = new FormData(form);
        const permissionData = Object.fromEntries(formData.entries());

        // Calculate duration
        permissionData.duration = this.calculatePermissionDuration(permissionData.start_time, permissionData.end_time);

        try {
            if (this.selectedPermission) {
                // Update existing permission
                Database.update('permissions', this.selectedPermission.id, permissionData);
                window.samApp.showAlert('تم تحديث طلب الإذن بنجاح', 'success');
            } else {
                // Create new permission
                Database.create('permissions', permissionData);
                window.samApp.showAlert('تم إضافة طلب الإذن بنجاح', 'success');
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('permissionModal'));
            modal.hide();
            this.loadPermissionsData();

        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    viewPermission(permissionId) {
        const permission = Database.getById('permissions', permissionId);
        if (!permission) {
            window.samApp.showAlert('طلب الإذن غير موجود', 'danger');
            return;
        }

        this.showPermissionDetails(permission);
    }

    showPermissionDetails(permission) {
        const modal = new bootstrap.Modal(document.getElementById('permissionDetailsModal'));
        const content = document.getElementById('permissionDetailsContent');
        const actions = document.getElementById('permissionActions');

        const employee = Database.getEmployee(permission.employee_id);
        const duration = this.calculatePermissionDuration(permission.start_time, permission.end_time);

        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات الموظف</h6>
                    <p><strong>الاسم:</strong> ${employee?.name || 'غير معروف'}</p>
                    <p><strong>الرقم الوظيفي:</strong> ${employee?.employee_number || ''}</p>
                    <p><strong>القسم:</strong> ${employee?.department || 'غير محدد'}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">تفاصيل الإذن</h6>
                    <p><strong>نوع الإذن:</strong> ${this.getPermissionTypeText(permission.permission_type)}</p>
                    <p><strong>التاريخ:</strong> ${window.samApp.formatDate(permission.permission_date)}</p>
                    <p><strong>من الساعة:</strong> ${permission.start_time || '-'}</p>
                    <p><strong>إلى الساعة:</strong> ${permission.end_time || '-'}</p>
                    <p><strong>المدة:</strong> ${duration} ساعة</p>
                    <p><strong>الحالة:</strong> ${this.getStatusBadge(permission.status)}</p>
                    <p><strong>يؤثر على الراتب:</strong> ${permission.affects_salary === 'yes' ? 'نعم' : 'لا'}</p>
                </div>
                <div class="col-12">
                    <h6 class="text-primary">سبب الإذن</h6>
                    <p>${permission.reason || 'غير محدد'}</p>
                </div>
                ${permission.admin_notes ? `
                <div class="col-12">
                    <h6 class="text-primary">ملاحظات الإدارة</h6>
                    <p>${permission.admin_notes}</p>
                </div>
                ` : ''}
            </div>
        `;

        // Add action buttons based on permission status
        if (permission.status === 'pending') {
            actions.innerHTML = `
                <button type="button" class="btn btn-success me-2" onclick="permissionsManager.approvePermission('${permission.id}')">
                    <i class="fas fa-check me-2"></i>موافقة
                </button>
                <button type="button" class="btn btn-danger" onclick="permissionsManager.rejectPermission('${permission.id}')">
                    <i class="fas fa-times me-2"></i>رفض
                </button>
            `;
        } else {
            actions.innerHTML = '';
        }

        modal.show();
    }

    approvePermission(permissionId) {
        try {
            Database.update('permissions', permissionId, { status: 'approved' });
            window.samApp.showAlert('تم الموافقة على طلب الإذن', 'success');

            const modal = bootstrap.Modal.getInstance(document.getElementById('permissionDetailsModal'));
            modal.hide();
            this.loadPermissionsData();

        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    rejectPermission(permissionId) {
        const reason = prompt('يرجى إدخال سبب الرفض:');
        if (reason) {
            try {
                Database.update('permissions', permissionId, {
                    status: 'rejected',
                    admin_notes: reason
                });
                window.samApp.showAlert('تم رفض طلب الإذن', 'success');

                const modal = bootstrap.Modal.getInstance(document.getElementById('permissionDetailsModal'));
                modal.hide();
                this.loadPermissionsData();

            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    deletePermission(permissionId) {
        if (confirm('هل أنت متأكد من حذف طلب الإذن؟')) {
            try {
                Database.delete('permissions', permissionId);
                window.samApp.showAlert('تم حذف طلب الإذن بنجاح', 'success');
                this.loadPermissionsData();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    exportPermissions() {
        const permissions = Database.getAll('permissions') || [];
        const employees = Database.getEmployees();

        const exportData = permissions.map(permission => {
            const employee = employees.find(emp => emp.id === permission.employee_id);
            const duration = this.calculatePermissionDuration(permission.start_time, permission.end_time);

            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'نوع الإذن': this.getPermissionTypeText(permission.permission_type),
                'التاريخ': permission.permission_date,
                'من الساعة': permission.start_time || '',
                'إلى الساعة': permission.end_time || '',
                'المدة (ساعات)': duration,
                'الحالة': permission.status,
                'يؤثر على الراتب': permission.affects_salary === 'yes' ? 'نعم' : 'لا',
                'السبب': permission.reason || '',
                'ملاحظات الإدارة': permission.admin_notes || '',
                'تاريخ الطلب': permission.created_at
            };
        });

        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الأذونات');
        XLSX.writeFile(wb, `permissions_${new Date().toISOString().split('T')[0]}.xlsx`);

        window.samApp.showAlert('تم تصدير بيانات الأذونات بنجاح', 'success');
    }
}

// Global reference for modal actions
let permissionsManager;
