/**
 * SAM - نظام إدارة شؤون الموظفين
 * Reports Module
 * وحدة التقارير
 */

class ReportsManager {
    constructor() {
        this.currentReport = null;
        this.reportData = null;
    }

    render() {
        if (!window.authManager.hasPermission('reports')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير والإحصائيات
                    </h2>
                </div>
            </div>

            <!-- Report Categories -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card h-100 report-card" data-report="employees">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5>تقارير الموظفين</h5>
                            <p class="text-muted">بيانات الموظفين والأقسام</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card h-100 report-card" data-report="attendance">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-3x text-success mb-3"></i>
                            <h5>تقارير الحضور</h5>
                            <p class="text-muted">الحضور والانصراف والتأخير</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card h-100 report-card" data-report="leaves">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-alt fa-3x text-warning mb-3"></i>
                            <h5>تقارير الإجازات</h5>
                            <p class="text-muted">الإجازات والأرصدة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card h-100 report-card" data-report="payroll">
                        <div class="card-body text-center">
                            <i class="fas fa-money-bill-wave fa-3x text-info mb-3"></i>
                            <h5>تقارير الرواتب</h5>
                            <p class="text-muted">الرواتب والمستحقات</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Filters -->
            <div class="card mb-4" id="reportFilters" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="filtersContainer">
                        <!-- Filters will be loaded here -->
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button class="btn btn-primary me-2" id="generateReportBtn">
                                <i class="fas fa-chart-line me-2"></i>
                                إنشاء التقرير
                            </button>
                            <button class="btn btn-outline-secondary" id="clearFiltersBtn">
                                <i class="fas fa-times me-2"></i>
                                مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Results -->
            <div class="card" id="reportResults" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0" id="reportTitle">
                        <i class="fas fa-chart-bar me-2"></i>
                        نتائج التقرير
                    </h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-success btn-sm" id="exportExcelBtn">
                            <i class="fas fa-file-excel me-2"></i>
                            Excel
                        </button>
                        <button class="btn btn-outline-danger btn-sm" id="exportPdfBtn">
                            <i class="fas fa-file-pdf me-2"></i>
                            PDF
                        </button>
                        <button class="btn btn-outline-primary btn-sm" id="printReportBtn">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Report Summary -->
                    <div id="reportSummary" class="mb-4">
                        <!-- Summary will be loaded here -->
                    </div>
                    
                    <!-- Report Chart -->
                    <div id="reportChart" class="mb-4" style="display: none;">
                        <canvas id="reportCanvas" height="100"></canvas>
                    </div>
                    
                    <!-- Report Table -->
                    <div id="reportTable">
                        <!-- Table will be loaded here -->
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Report category selection
        document.querySelectorAll('.report-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const reportType = e.currentTarget.dataset.report;
                this.selectReportType(reportType);
            });
        });

        // Generate report button
        document.getElementById('generateReportBtn').addEventListener('click', () => {
            this.generateReport();
        });

        // Clear filters button
        document.getElementById('clearFiltersBtn').addEventListener('click', () => {
            this.clearFilters();
        });

        // Export buttons
        document.getElementById('exportExcelBtn').addEventListener('click', () => {
            this.exportToExcel();
        });

        document.getElementById('exportPdfBtn').addEventListener('click', () => {
            this.exportToPDF();
        });

        document.getElementById('printReportBtn').addEventListener('click', () => {
            this.printReport();
        });
    }

    selectReportType(reportType) {
        this.currentReport = reportType;
        
        // Update UI
        document.querySelectorAll('.report-card').forEach(card => {
            card.classList.remove('border-primary');
        });
        document.querySelector(`[data-report="${reportType}"]`).classList.add('border-primary');
        
        // Show filters
        this.showReportFilters(reportType);
        
        // Hide results
        document.getElementById('reportResults').style.display = 'none';
    }

    showReportFilters(reportType) {
        const filtersContainer = document.getElementById('filtersContainer');
        const reportFilters = document.getElementById('reportFilters');
        
        let filtersHTML = '';
        
        switch (reportType) {
            case 'employees':
                filtersHTML = this.getEmployeeFilters();
                break;
            case 'attendance':
                filtersHTML = this.getAttendanceFilters();
                break;
            case 'leaves':
                filtersHTML = this.getLeaveFilters();
                break;
            case 'payroll':
                filtersHTML = this.getPayrollFilters();
                break;
        }
        
        filtersContainer.innerHTML = filtersHTML;
        reportFilters.style.display = 'block';
        
        // Load filter options
        this.loadFilterOptions();
    }

    getEmployeeFilters() {
        return `
            <div class="col-md-3 mb-3">
                <label class="form-label">القسم</label>
                <select class="form-select" id="departmentFilter">
                    <option value="">جميع الأقسام</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">المنصب</label>
                <select class="form-select" id="positionFilter">
                    <option value="">جميع المناصب</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="terminated">مفصول</option>
                    <option value="suspended">موقوف</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">نوع التقرير</label>
                <select class="form-select" id="reportSubType">
                    <option value="summary">ملخص الموظفين</option>
                    <option value="detailed">تفاصيل الموظفين</option>
                    <option value="by_department">حسب القسم</option>
                    <option value="by_position">حسب المنصب</option>
                </select>
            </div>
        `;
    }

    getAttendanceFilters() {
        return `
            <div class="col-md-3 mb-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="startDate">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="endDate">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">الموظف</label>
                <select class="form-select" id="employeeFilter">
                    <option value="">جميع الموظفين</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">نوع التقرير</label>
                <select class="form-select" id="reportSubType">
                    <option value="daily">تقرير يومي</option>
                    <option value="monthly">تقرير شهري</option>
                    <option value="late_report">تقرير التأخير</option>
                    <option value="absence_report">تقرير الغياب</option>
                </select>
            </div>
        `;
    }

    getLeaveFilters() {
        return `
            <div class="col-md-3 mb-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="startDate">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="endDate">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">الموظف</label>
                <select class="form-select" id="employeeFilter">
                    <option value="">جميع الموظفين</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">نوع الإجازة</label>
                <select class="form-select" id="leaveTypeFilter">
                    <option value="">جميع الأنواع</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="pending">في الانتظار</option>
                    <option value="approved">موافق عليها</option>
                    <option value="rejected">مرفوضة</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">نوع التقرير</label>
                <select class="form-select" id="reportSubType">
                    <option value="summary">ملخص الإجازات</option>
                    <option value="detailed">تفاصيل الإجازات</option>
                    <option value="balance">أرصدة الإجازات</option>
                </select>
            </div>
        `;
    }

    getPayrollFilters() {
        return `
            <div class="col-md-3 mb-3">
                <label class="form-label">من شهر</label>
                <input type="month" class="form-control" id="startMonth">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">إلى شهر</label>
                <input type="month" class="form-control" id="endMonth">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">الموظف</label>
                <select class="form-select" id="employeeFilter">
                    <option value="">جميع الموظفين</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">نوع التقرير</label>
                <select class="form-select" id="reportSubType">
                    <option value="summary">ملخص الرواتب</option>
                    <option value="detailed">تفاصيل الرواتب</option>
                    <option value="comparison">مقارنة الرواتب</option>
                </select>
            </div>
        `;
    }

    loadFilterOptions() {
        // Load departments
        const departments = Database.getAll('departments');
        const departmentSelect = document.getElementById('departmentFilter');
        if (departmentSelect) {
            departments.forEach(dept => {
                departmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
            });
        }

        // Load positions
        const positions = Database.getAll('positions');
        const positionSelect = document.getElementById('positionFilter');
        if (positionSelect) {
            positions.forEach(pos => {
                positionSelect.innerHTML += `<option value="${pos.id}">${pos.name}</option>`;
            });
        }

        // Load employees
        const employees = Database.getEmployees();
        const employeeSelect = document.getElementById('employeeFilter');
        if (employeeSelect) {
            employees.forEach(emp => {
                employeeSelect.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        }

        // Load leave types
        const leaveTypes = Database.getAll('leave_types');
        const leaveTypeSelect = document.getElementById('leaveTypeFilter');
        if (leaveTypeSelect) {
            leaveTypes.forEach(type => {
                leaveTypeSelect.innerHTML += `<option value="${type.id}">${type.name}</option>`;
            });
        }

        // Set default dates
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        
        if (startDateInput) startDateInput.value = firstDayOfMonth.toISOString().split('T')[0];
        if (endDateInput) endDateInput.value = lastDayOfMonth.toISOString().split('T')[0];
        
        // Set default months
        const currentMonth = today.toISOString().substring(0, 7);
        const startMonthInput = document.getElementById('startMonth');
        const endMonthInput = document.getElementById('endMonth');
        
        if (startMonthInput) startMonthInput.value = currentMonth;
        if (endMonthInput) endMonthInput.value = currentMonth;
    }

    generateReport() {
        if (!this.currentReport) {
            window.samApp.showAlert('يرجى اختيار نوع التقرير أولاً', 'warning');
            return;
        }

        const filters = this.getFilterValues();

        switch (this.currentReport) {
            case 'employees':
                this.generateEmployeeReport(filters);
                break;
            case 'attendance':
                this.generateAttendanceReport(filters);
                break;
            case 'leaves':
                this.generateLeaveReport(filters);
                break;
            case 'payroll':
                this.generatePayrollReport(filters);
                break;
        }
    }

    getFilterValues() {
        const filters = {};

        // Get all filter inputs
        const filterInputs = document.querySelectorAll('#filtersContainer input, #filtersContainer select');
        filterInputs.forEach(input => {
            if (input.value) {
                filters[input.id] = input.value;
            }
        });

        return filters;
    }

    generateEmployeeReport(filters) {
        let employees = Database.getEmployees();

        // Apply filters
        if (filters.departmentFilter) {
            employees = employees.filter(emp => emp.department === filters.departmentFilter);
        }
        if (filters.positionFilter) {
            employees = employees.filter(emp => emp.position === filters.positionFilter);
        }
        if (filters.statusFilter) {
            employees = employees.filter(emp => emp.status === filters.statusFilter);
        }

        this.reportData = employees;
        this.displayEmployeeReport(employees, filters.reportSubType || 'summary');
    }

    displayEmployeeReport(employees, subType) {
        const reportResults = document.getElementById('reportResults');
        const reportTitle = document.getElementById('reportTitle');
        const reportSummary = document.getElementById('reportSummary');
        const reportTable = document.getElementById('reportTable');

        reportTitle.innerHTML = '<i class="fas fa-users me-2"></i>تقرير الموظفين';

        // Summary
        const departments = Database.getAll('departments');
        const positions = Database.getAll('positions');

        const summary = {
            total: employees.length,
            active: employees.filter(emp => emp.status === 'active').length,
            inactive: employees.filter(emp => emp.status === 'inactive').length,
            departments: [...new Set(employees.map(emp => emp.department))].length,
            avgSalary: employees.reduce((sum, emp) => sum + (emp.salary || 0), 0) / employees.length || 0
        };

        reportSummary.innerHTML = `
            <div class="row">
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-primary">${summary.total}</h4>
                        <small>إجمالي الموظفين</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-success">${summary.active}</h4>
                        <small>نشط</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-secondary">${summary.inactive}</h4>
                        <small>غير نشط</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-info">${summary.departments}</h4>
                        <small>عدد الأقسام</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-warning">${window.samApp.formatCurrency(summary.avgSalary)}</h4>
                        <small>متوسط الراتب</small>
                    </div>
                </div>
            </div>
        `;

        // Table
        let tableHTML = '';

        if (subType === 'summary') {
            tableHTML = this.generateEmployeeSummaryTable(employees);
        } else if (subType === 'detailed') {
            tableHTML = this.generateEmployeeDetailedTable(employees);
        } else if (subType === 'by_department') {
            tableHTML = this.generateEmployeeByDepartmentTable(employees);
        } else if (subType === 'by_position') {
            tableHTML = this.generateEmployeeByPositionTable(employees);
        }

        reportTable.innerHTML = tableHTML;
        reportResults.style.display = 'block';
    }

    generateEmployeeSummaryTable(employees) {
        const departments = Database.getAll('departments');
        const positions = Database.getAll('positions');

        return `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الرقم الوظيفي</th>
                            <th>الاسم</th>
                            <th>القسم</th>
                            <th>المنصب</th>
                            <th>تاريخ التعيين</th>
                            <th>الراتب</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${employees.map(emp => {
                            const department = departments.find(d => d.id === emp.department);
                            const position = positions.find(p => p.id === emp.position);
                            return `
                                <tr>
                                    <td>${emp.employee_number}</td>
                                    <td>${emp.name}</td>
                                    <td>${department?.name || 'غير محدد'}</td>
                                    <td>${position?.name || 'غير محدد'}</td>
                                    <td>${window.samApp.formatDate(emp.hire_date)}</td>
                                    <td>${window.samApp.formatCurrency(emp.salary || 0)}</td>
                                    <td><span class="badge bg-${emp.status === 'active' ? 'success' : 'secondary'}">${emp.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    generateAttendanceReport(filters) {
        let attendance = Database.getAll('attendance');

        // Apply date filters
        if (filters.startDate) {
            attendance = attendance.filter(a => a.date >= filters.startDate);
        }
        if (filters.endDate) {
            attendance = attendance.filter(a => a.date <= filters.endDate);
        }
        if (filters.employeeFilter) {
            attendance = attendance.filter(a => a.employee_id === filters.employeeFilter);
        }

        this.reportData = attendance;
        this.displayAttendanceReport(attendance, filters.reportSubType || 'daily');
    }

    displayAttendanceReport(attendance, subType) {
        const reportResults = document.getElementById('reportResults');
        const reportTitle = document.getElementById('reportTitle');
        const reportSummary = document.getElementById('reportSummary');
        const reportTable = document.getElementById('reportTable');

        reportTitle.innerHTML = '<i class="fas fa-clock me-2"></i>تقرير الحضور والانصراف';

        // Summary
        const summary = {
            total: attendance.length,
            present: attendance.filter(a => a.status === 'present').length,
            late: attendance.filter(a => a.status === 'late').length,
            absent: attendance.filter(a => a.status === 'absent').length,
            attendanceRate: attendance.length > 0 ? (attendance.filter(a => a.status !== 'absent').length / attendance.length * 100).toFixed(1) : 0
        };

        reportSummary.innerHTML = `
            <div class="row">
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-primary">${summary.total}</h4>
                        <small>إجمالي السجلات</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-success">${summary.present}</h4>
                        <small>حاضر</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-warning">${summary.late}</h4>
                        <small>متأخر</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-danger">${summary.absent}</h4>
                        <small>غائب</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-info">${summary.attendanceRate}%</h4>
                        <small>معدل الحضور</small>
                    </div>
                </div>
            </div>
        `;

        // Generate table based on sub type
        let tableHTML = '';
        if (subType === 'daily') {
            tableHTML = this.generateDailyAttendanceTable(attendance);
        } else if (subType === 'monthly') {
            tableHTML = this.generateMonthlyAttendanceTable(attendance);
        } else if (subType === 'late_report') {
            tableHTML = this.generateLateReportTable(attendance.filter(a => a.status === 'late'));
        }

        reportTable.innerHTML = tableHTML;
        reportResults.style.display = 'block';
    }

    generateDailyAttendanceTable(attendance) {
        const employees = Database.getEmployees();

        return `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>التاريخ</th>
                            <th>وقت الحضور</th>
                            <th>وقت الانصراف</th>
                            <th>ساعات العمل</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${attendance.map(record => {
                            const employee = employees.find(emp => emp.id === record.employee_id);
                            const workingHours = this.calculateWorkingHours(record.check_in, record.check_out);
                            return `
                                <tr>
                                    <td>${employee?.name || 'غير معروف'}</td>
                                    <td>${window.samApp.formatDate(record.date)}</td>
                                    <td>${record.check_in || '-'}</td>
                                    <td>${record.check_out || '-'}</td>
                                    <td>${workingHours}</td>
                                    <td><span class="badge bg-${this.getStatusColor(record.status)}">${this.getStatusText(record.status)}</span></td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    calculateWorkingHours(checkIn, checkOut) {
        if (!checkIn || !checkOut) return '-';

        const start = new Date(`2000-01-01T${checkIn}`);
        const end = new Date(`2000-01-01T${checkOut}`);
        const diffMs = end - start;
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

        return `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`;
    }

    getStatusColor(status) {
        const colorMap = {
            'present': 'success',
            'late': 'warning',
            'absent': 'danger',
            'early': 'info'
        };
        return colorMap[status] || 'secondary';
    }

    getStatusText(status) {
        const textMap = {
            'present': 'حاضر',
            'late': 'متأخر',
            'absent': 'غائب',
            'early': 'مبكر'
        };
        return textMap[status] || 'غير محدد';
    }

    clearFilters() {
        const filterInputs = document.querySelectorAll('#filtersContainer input, #filtersContainer select');
        filterInputs.forEach(input => {
            if (input.type === 'select-one') {
                input.selectedIndex = 0;
            } else {
                input.value = '';
            }
        });

        // Reset default dates
        this.loadFilterOptions();
    }

    exportToExcel() {
        if (!this.reportData) {
            window.samApp.showAlert('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        let exportData = [];
        let filename = 'report';

        switch (this.currentReport) {
            case 'employees':
                exportData = this.prepareEmployeeExportData();
                filename = 'employees_report';
                break;
            case 'attendance':
                exportData = this.prepareAttendanceExportData();
                filename = 'attendance_report';
                break;
            case 'leaves':
                exportData = this.prepareLeaveExportData();
                filename = 'leaves_report';
                break;
            case 'payroll':
                exportData = this.preparePayrollExportData();
                filename = 'payroll_report';
                break;
        }

        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'التقرير');
        XLSX.writeFile(wb, `${filename}_${new Date().toISOString().split('T')[0]}.xlsx`);

        window.samApp.showAlert('تم تصدير التقرير بنجاح', 'success');
    }

    prepareEmployeeExportData() {
        const departments = Database.getAll('departments');
        const positions = Database.getAll('positions');

        return this.reportData.map(emp => {
            const department = departments.find(d => d.id === emp.department);
            const position = positions.find(p => p.id === emp.position);

            return {
                'الرقم الوظيفي': emp.employee_number,
                'الاسم': emp.name,
                'رقم الهوية': emp.national_id,
                'البريد الإلكتروني': emp.email,
                'الهاتف': emp.phone,
                'القسم': department?.name || 'غير محدد',
                'المنصب': position?.name || 'غير محدد',
                'تاريخ التعيين': emp.hire_date,
                'الراتب': emp.salary,
                'الحالة': emp.status
            };
        });
    }

    prepareAttendanceExportData() {
        const employees = Database.getEmployees();

        return this.reportData.map(record => {
            const employee = employees.find(emp => emp.id === record.employee_id);
            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'التاريخ': record.date,
                'وقت الحضور': record.check_in || '',
                'وقت الانصراف': record.check_out || '',
                'ساعات العمل': this.calculateWorkingHours(record.check_in, record.check_out),
                'الحالة': this.getStatusText(record.status)
            };
        });
    }

    exportToPDF() {
        if (!this.reportData) {
            window.samApp.showAlert('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        // Create PDF content
        const reportContent = document.getElementById('reportResults').cloneNode(true);

        // Remove action buttons from PDF
        const actionButtons = reportContent.querySelectorAll('.btn-group');
        actionButtons.forEach(btn => btn.remove());

        // Create print window
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>تقرير - ${this.getReportTitle()}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                    .card { border: 1px solid #ddd; margin-bottom: 20px; }
                    .card-header { background-color: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }
                    .card-body { padding: 15px; }
                    .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    .table th { background-color: #f5f5f5; }
                    .badge { padding: 4px 8px; border-radius: 4px; color: white; }
                    .bg-success { background-color: #28a745; }
                    .bg-warning { background-color: #ffc107; color: black; }
                    .bg-danger { background-color: #dc3545; }
                    .bg-info { background-color: #17a2b8; }
                    .bg-secondary { background-color: #6c757d; }
                    .text-center { text-align: center; }
                    .row { display: flex; flex-wrap: wrap; }
                    .col-md-2, .col-md-3 { flex: 1; padding: 10px; }
                    @media print {
                        body { margin: 0; }
                        .card { border: none; box-shadow: none; }
                    }
                </style>
            </head>
            <body>
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1>نظام إدارة شؤون الموظفين - SAM</h1>
                    <h2>${this.getReportTitle()}</h2>
                    <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>
                ${reportContent.innerHTML}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    printReport() {
        this.exportToPDF();
    }

    getReportTitle() {
        const titles = {
            'employees': 'تقرير الموظفين',
            'attendance': 'تقرير الحضور والانصراف',
            'leaves': 'تقرير الإجازات',
            'payroll': 'تقرير الرواتب'
        };
        return titles[this.currentReport] || 'تقرير';
    }

    // Additional report generation methods for leaves and payroll
    generateLeaveReport(filters) {
        let leaves = Database.getLeaves();

        // Apply filters
        if (filters.startDate) {
            leaves = leaves.filter(l => l.start_date >= filters.startDate);
        }
        if (filters.endDate) {
            leaves = leaves.filter(l => l.end_date <= filters.endDate);
        }
        if (filters.employeeFilter) {
            leaves = leaves.filter(l => l.employee_id === filters.employeeFilter);
        }
        if (filters.leaveTypeFilter) {
            leaves = leaves.filter(l => l.leave_type === filters.leaveTypeFilter);
        }
        if (filters.statusFilter) {
            leaves = leaves.filter(l => l.status === filters.statusFilter);
        }

        this.reportData = leaves;
        this.displayLeaveReport(leaves, filters.reportSubType || 'summary');
    }

    displayLeaveReport(leaves, subType) {
        const reportResults = document.getElementById('reportResults');
        const reportTitle = document.getElementById('reportTitle');
        const reportSummary = document.getElementById('reportSummary');
        const reportTable = document.getElementById('reportTable');

        reportTitle.innerHTML = '<i class="fas fa-calendar-alt me-2"></i>تقرير الإجازات';

        // Summary
        const summary = {
            total: leaves.length,
            pending: leaves.filter(l => l.status === 'pending').length,
            approved: leaves.filter(l => l.status === 'approved').length,
            rejected: leaves.filter(l => l.status === 'rejected').length,
            totalDays: leaves.reduce((sum, l) => sum + (l.days || 0), 0)
        };

        reportSummary.innerHTML = `
            <div class="row">
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-primary">${summary.total}</h4>
                        <small>إجمالي الطلبات</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-warning">${summary.pending}</h4>
                        <small>في الانتظار</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-success">${summary.approved}</h4>
                        <small>موافق عليها</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-danger">${summary.rejected}</h4>
                        <small>مرفوضة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-info">${summary.totalDays}</h4>
                        <small>إجمالي الأيام</small>
                    </div>
                </div>
            </div>
        `;

        // Generate table
        reportTable.innerHTML = this.generateLeaveTable(leaves);
        reportResults.style.display = 'block';
    }

    generateLeaveTable(leaves) {
        const employees = Database.getEmployees();
        const leaveTypes = Database.getAll('leave_types');

        return `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>عدد الأيام</th>
                            <th>الحالة</th>
                            <th>السبب</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${leaves.map(leave => {
                            const employee = employees.find(emp => emp.id === leave.employee_id);
                            const leaveType = leaveTypes.find(type => type.id === leave.leave_type);
                            return `
                                <tr>
                                    <td>${employee?.name || 'غير معروف'}</td>
                                    <td>${leaveType?.name || 'غير محدد'}</td>
                                    <td>${window.samApp.formatDate(leave.start_date)}</td>
                                    <td>${window.samApp.formatDate(leave.end_date)}</td>
                                    <td>${leave.days || 0}</td>
                                    <td><span class="badge bg-${this.getLeaveStatusColor(leave.status)}">${this.getLeaveStatusText(leave.status)}</span></td>
                                    <td>${leave.reason || '-'}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    getLeaveStatusColor(status) {
        const colorMap = {
            'pending': 'warning',
            'approved': 'success',
            'rejected': 'danger'
        };
        return colorMap[status] || 'secondary';
    }

    getLeaveStatusText(status) {
        const textMap = {
            'pending': 'في الانتظار',
            'approved': 'موافق عليها',
            'rejected': 'مرفوضة'
        };
        return textMap[status] || 'غير محدد';
    }

    generatePayrollReport(filters) {
        let payrolls = Database.getAll('payroll');

        // Apply filters
        if (filters.startMonth) {
            payrolls = payrolls.filter(p => p.month >= filters.startMonth);
        }
        if (filters.endMonth) {
            payrolls = payrolls.filter(p => p.month <= filters.endMonth);
        }
        if (filters.employeeFilter) {
            payrolls = payrolls.filter(p => p.employee_id === filters.employeeFilter);
        }

        this.reportData = payrolls;
        this.displayPayrollReport(payrolls, filters.reportSubType || 'summary');
    }

    displayPayrollReport(payrolls, subType) {
        const reportResults = document.getElementById('reportResults');
        const reportTitle = document.getElementById('reportTitle');
        const reportSummary = document.getElementById('reportSummary');
        const reportTable = document.getElementById('reportTable');

        reportTitle.innerHTML = '<i class="fas fa-money-bill-wave me-2"></i>تقرير الرواتب';

        // Summary
        const summary = {
            total: payrolls.length,
            totalAmount: payrolls.reduce((sum, p) => sum + (p.net_salary || 0), 0),
            paid: payrolls.filter(p => p.status === 'paid').length,
            pending: payrolls.filter(p => p.status === 'pending').length,
            avgSalary: payrolls.length > 0 ? payrolls.reduce((sum, p) => sum + (p.net_salary || 0), 0) / payrolls.length : 0
        };

        reportSummary.innerHTML = `
            <div class="row">
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-primary">${summary.total}</h4>
                        <small>عدد الكشوف</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-success">${window.samApp.formatCurrency(summary.totalAmount)}</h4>
                        <small>إجمالي الرواتب</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-info">${summary.paid}</h4>
                        <small>مدفوعة</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h4 class="text-warning">${summary.pending}</h4>
                        <small>في الانتظار</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-secondary">${window.samApp.formatCurrency(summary.avgSalary)}</h4>
                        <small>متوسط الراتب</small>
                    </div>
                </div>
            </div>
        `;

        // Generate table
        reportTable.innerHTML = this.generatePayrollTable(payrolls);
        reportResults.style.display = 'block';
    }

    generatePayrollTable(payrolls) {
        const employees = Database.getEmployees();

        return `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>الشهر</th>
                            <th>الراتب الأساسي</th>
                            <th>البدلات</th>
                            <th>الحوافز</th>
                            <th>الخصومات</th>
                            <th>صافي الراتب</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${payrolls.map(payroll => {
                            const employee = employees.find(emp => emp.id === payroll.employee_id);
                            const allowances = (payroll.housing_allowance || 0) + (payroll.transport_allowance || 0) + (payroll.food_allowance || 0) + (payroll.other_allowances || 0);
                            const bonuses = (payroll.performance_bonus || 0) + (payroll.other_bonuses || 0);
                            return `
                                <tr>
                                    <td>${employee?.name || 'غير معروف'}</td>
                                    <td>${this.formatMonth(payroll.month)}</td>
                                    <td>${window.samApp.formatCurrency(payroll.basic_salary || 0)}</td>
                                    <td>${window.samApp.formatCurrency(allowances)}</td>
                                    <td>${window.samApp.formatCurrency(bonuses)}</td>
                                    <td>${window.samApp.formatCurrency(payroll.total_deductions || 0)}</td>
                                    <td><strong>${window.samApp.formatCurrency(payroll.net_salary || 0)}</strong></td>
                                    <td><span class="badge bg-${this.getPayrollStatusColor(payroll.status)}">${this.getPayrollStatusText(payroll.status)}</span></td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    formatMonth(monthStr) {
        const [year, month] = monthStr.split('-');
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[parseInt(month) - 1]} ${year}`;
    }

    getPayrollStatusColor(status) {
        const colorMap = {
            'pending': 'warning',
            'paid': 'success',
            'cancelled': 'danger'
        };
        return colorMap[status] || 'secondary';
    }

    getPayrollStatusText(status) {
        const textMap = {
            'pending': 'في الانتظار',
            'paid': 'مدفوعة',
            'cancelled': 'ملغية'
        };
        return textMap[status] || 'غير محدد';
    }

    prepareLeaveExportData() {
        const employees = Database.getEmployees();
        const leaveTypes = Database.getAll('leave_types');

        return this.reportData.map(leave => {
            const employee = employees.find(emp => emp.id === leave.employee_id);
            const leaveType = leaveTypes.find(type => type.id === leave.leave_type);

            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'نوع الإجازة': leaveType?.name || 'غير محدد',
                'تاريخ البداية': leave.start_date,
                'تاريخ النهاية': leave.end_date,
                'عدد الأيام': leave.days,
                'الحالة': this.getLeaveStatusText(leave.status),
                'السبب': leave.reason || '',
                'ملاحظات الإدارة': leave.admin_notes || ''
            };
        });
    }

    preparePayrollExportData() {
        const employees = Database.getEmployees();

        return this.reportData.map(payroll => {
            const employee = employees.find(emp => emp.id === payroll.employee_id);
            const allowances = (payroll.housing_allowance || 0) + (payroll.transport_allowance || 0) + (payroll.food_allowance || 0) + (payroll.other_allowances || 0);
            const bonuses = (payroll.performance_bonus || 0) + (payroll.other_bonuses || 0);

            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'الشهر': this.formatMonth(payroll.month),
                'الراتب الأساسي': payroll.basic_salary,
                'البدلات': allowances,
                'الحوافز': bonuses,
                'إجمالي الاستحقاقات': payroll.gross_salary,
                'الخصومات': payroll.total_deductions,
                'صافي الراتب': payroll.net_salary,
                'الحالة': this.getPayrollStatusText(payroll.status),
                'تاريخ الدفع': payroll.payment_date || ''
            };
        });
    }
}
