/**
 * SAM - نظام إدارة شؤون الموظفين
 * Employee Management Module
 * وحدة إدارة الموظفين
 */

class EmployeeManager {
    constructor() {
        this.currentView = 'list';
        this.selectedEmployee = null;
        this.searchQuery = '';
        this.filterDepartment = '';
        this.filterStatus = '';
    }

    render() {
        if (!window.authManager.hasPermission('employees')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadEmployeesList();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-users me-2"></i>
                            إدارة الموظفين
                        </h2>
                        <button class="btn btn-primary" id="addEmployeeBtn">
                            <i class="fas fa-plus me-2"></i>
                            إضافة موظف جديد
                        </button>
                    </div>
                </div>
            </div>

            <!-- Search and Filter Bar -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="البحث بالاسم، الرقم الوظيفي، أو البريد الإلكتروني...">
                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="departmentFilter">
                        <option value="">جميع الأقسام</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="terminated">مفصول</option>
                        <option value="suspended">موقوف</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary" id="exportBtn">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-info" id="printBtn">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Employees List -->
            <div class="row" id="employeesContainer">
                <!-- Employees will be loaded here -->
            </div>

            <!-- Employee Modal -->
            <div class="modal fade" id="employeeModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="employeeModalTitle">إضافة موظف جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="employeeForm">
                                <div class="row">
                                    <!-- Personal Information -->
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-user me-2"></i>
                                            المعلومات الشخصية
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهوية *</label>
                                        <input type="text" class="form-control" name="national_id" required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الميلاد</label>
                                        <input type="date" class="form-control" name="birth_date">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الجنس</label>
                                        <select class="form-select" name="gender">
                                            <option value="">اختر الجنس</option>
                                            <option value="male">ذكر</option>
                                            <option value="female">أنثى</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label class="form-label">العنوان</label>
                                        <textarea class="form-control" name="address" rows="2"></textarea>
                                    </div>

                                    <!-- Work Information -->
                                    <div class="col-12 mt-4">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-briefcase me-2"></i>
                                            معلومات العمل
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الرقم الوظيفي</label>
                                        <input type="text" class="form-control" name="employee_number" readonly>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ التعيين *</label>
                                        <input type="date" class="form-control" name="hire_date" required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">القسم *</label>
                                        <select class="form-select" name="department" required>
                                            <option value="">اختر القسم</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المنصب *</label>
                                        <select class="form-select" name="position" required>
                                            <option value="">اختر المنصب</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الراتب الأساسي</label>
                                        <input type="number" class="form-control" name="salary" min="0" step="0.01">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">حالة الموظف</label>
                                        <select class="form-select" name="status">
                                            <option value="active">نشط</option>
                                            <option value="inactive">غير نشط</option>
                                            <option value="terminated">مفصول</option>
                                            <option value="suspended">موقوف</option>
                                        </select>
                                    </div>

                                    <!-- Work Schedule Section -->
                                    <div class="col-12 mt-4">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-clock me-2"></i>
                                            جدول العمل
                                        </h6>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">وقت بداية العمل</label>
                                        <input type="time" class="form-control" name="work_start_time" value="08:00">
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">وقت نهاية العمل</label>
                                        <input type="time" class="form-control" name="work_end_time" value="17:00">
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">فترة الراحة (دقيقة)</label>
                                        <input type="number" class="form-control" name="break_duration" value="60" min="0" max="180">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">فترة السماح للتأخير (دقيقة)</label>
                                        <input type="number" class="form-control" name="late_tolerance" value="15" min="0" max="60">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">أيام العمل</label>
                                        <div class="form-check-group">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" name="working_days" value="sunday" id="sunday" checked>
                                                <label class="form-check-label" for="sunday">الأحد</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" name="working_days" value="monday" id="monday" checked>
                                                <label class="form-check-label" for="monday">الاثنين</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" name="working_days" value="tuesday" id="tuesday" checked>
                                                <label class="form-check-label" for="tuesday">الثلاثاء</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" name="working_days" value="wednesday" id="wednesday" checked>
                                                <label class="form-check-label" for="wednesday">الأربعاء</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" name="working_days" value="thursday" id="thursday" checked>
                                                <label class="form-check-label" for="thursday">الخميس</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" name="working_days" value="friday" id="friday">
                                                <label class="form-check-label" for="friday">الجمعة</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" name="working_days" value="saturday" id="saturday">
                                                <label class="form-check-label" for="saturday">السبت</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Photo Upload -->
                                    <div class="col-12 mt-4">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-camera me-2"></i>
                                            الصورة الشخصية
                                        </h6>
                                        <div class="mb-3">
                                            <input type="file" class="form-control" name="photo" accept="image/*">
                                            <div class="form-text">يُفضل صورة بحجم 300x300 بكسل</div>
                                        </div>
                                        <div id="photoPreview" class="text-center" style="display: none;">
                                            <img id="previewImage" src="" alt="معاينة الصورة" 
                                                 style="max-width: 150px; max-height: 150px; border-radius: 50%;">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="saveEmployeeBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employee Details Modal -->
            <div class="modal fade" id="employeeDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل الموظف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="employeeDetailsContent">
                            <!-- Employee details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="editEmployeeBtn">تعديل</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Add employee button
        document.getElementById('addEmployeeBtn').addEventListener('click', () => {
            this.showEmployeeModal();
        });

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.loadEmployeesList();
        });

        document.getElementById('searchBtn').addEventListener('click', () => {
            this.loadEmployeesList();
        });

        // Filter functionality
        document.getElementById('departmentFilter').addEventListener('change', (e) => {
            this.filterDepartment = e.target.value;
            this.loadEmployeesList();
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.filterStatus = e.target.value;
            this.loadEmployeesList();
        });

        // Export button
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportEmployees();
        });

        // Print button
        document.getElementById('printBtn').addEventListener('click', () => {
            this.printEmployees();
        });

        // Save employee
        document.getElementById('saveEmployeeBtn').addEventListener('click', () => {
            this.saveEmployee();
        });

        // Photo preview
        document.querySelector('input[name="photo"]').addEventListener('change', (e) => {
            this.previewPhoto(e.target.files[0]);
        });

        // Edit employee from details modal
        document.getElementById('editEmployeeBtn').addEventListener('click', () => {
            if (this.selectedEmployee) {
                const detailsModal = bootstrap.Modal.getInstance(document.getElementById('employeeDetailsModal'));
                detailsModal.hide();
                this.showEmployeeModal(this.selectedEmployee);
            }
        });
    }

    loadEmployeesList() {
        let employees = Database.getEmployees();
        
        // Apply search filter
        if (this.searchQuery) {
            employees = Database.searchEmployees(this.searchQuery);
        }
        
        // Apply department filter
        if (this.filterDepartment) {
            employees = employees.filter(emp => emp.department === this.filterDepartment);
        }
        
        // Apply status filter
        if (this.filterStatus) {
            employees = employees.filter(emp => emp.status === this.filterStatus);
        }
        
        this.renderEmployeesList(employees);
        this.loadFilterOptions();
    }

    renderEmployeesList(employees) {
        const container = document.getElementById('employeesContainer');
        
        if (employees.length === 0) {
            container.innerHTML = `
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد موظفين</h4>
                        <p class="text-muted">ابدأ بإضافة موظف جديد</p>
                    </div>
                </div>
            `;
            return;
        }
        
        const employeesHTML = employees.map(employee => this.getEmployeeCardHTML(employee)).join('');
        container.innerHTML = employeesHTML;
        
        // Bind employee card events
        this.bindEmployeeCardEvents();
    }

    getEmployeeCardHTML(employee) {
        const departments = Database.getAll('departments');
        const positions = Database.getAll('positions');
        
        const department = departments.find(d => d.id === employee.department);
        const position = positions.find(p => p.id === employee.position);
        
        const statusBadge = this.getStatusBadge(employee.status);
        const photoSrc = employee.photo || 'https://via.placeholder.com/80x80/007bff/ffffff?text=صورة';
        
        return `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card employee-card h-100">
                    <div class="card-body text-center">
                        <img src="${photoSrc}" alt="${employee.name}" class="employee-avatar mb-3">
                        <h5 class="card-title">${employee.name}</h5>
                        <p class="text-muted mb-2">
                            <small>رقم وظيفي: ${employee.employee_number}</small>
                        </p>
                        <p class="card-text">
                            <strong>${position ? position.name : 'غير محدد'}</strong><br>
                            <small class="text-muted">${department ? department.name : 'غير محدد'}</small>
                        </p>
                        ${statusBadge}
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary me-2 view-employee" 
                                    data-employee-id="${employee.id}">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                            <button class="btn btn-sm btn-outline-success me-2 edit-employee" 
                                    data-employee-id="${employee.id}">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-employee" 
                                    data-employee-id="${employee.id}">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getStatusBadge(status) {
        const statusMap = {
            'active': { class: 'success', text: 'نشط' },
            'inactive': { class: 'secondary', text: 'غير نشط' },
            'terminated': { class: 'danger', text: 'مفصول' },
            'suspended': { class: 'warning', text: 'موقوف' }
        };
        
        const statusInfo = statusMap[status] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    bindEmployeeCardEvents() {
        // View employee
        document.querySelectorAll('.view-employee').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const employeeId = e.target.closest('.view-employee').dataset.employeeId;
                this.viewEmployee(employeeId);
            });
        });

        // Edit employee
        document.querySelectorAll('.edit-employee').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const employeeId = e.target.closest('.edit-employee').dataset.employeeId;
                const employee = Database.getEmployee(employeeId);
                this.showEmployeeModal(employee);
            });
        });

        // Delete employee
        document.querySelectorAll('.delete-employee').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const employeeId = e.target.closest('.delete-employee').dataset.employeeId;
                this.deleteEmployee(employeeId);
            });
        });
    }

    loadFilterOptions() {
        // Load departments
        const departments = Database.getAll('departments');
        const departmentSelect = document.getElementById('departmentFilter');
        departmentSelect.innerHTML = '<option value="">جميع الأقسام</option>';
        departments.forEach(dept => {
            departmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
        });
        departmentSelect.value = this.filterDepartment;

        // Load departments and positions in modal
        this.loadModalOptions();
    }

    loadModalOptions() {
        const departments = Database.getAll('departments');
        const positions = Database.getAll('positions');
        
        const departmentSelect = document.querySelector('#employeeModal select[name="department"]');
        const positionSelect = document.querySelector('#employeeModal select[name="position"]');
        
        departmentSelect.innerHTML = '<option value="">اختر القسم</option>';
        departments.forEach(dept => {
            departmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
        });
        
        positionSelect.innerHTML = '<option value="">اختر المنصب</option>';
        positions.forEach(pos => {
            positionSelect.innerHTML += `<option value="${pos.id}">${pos.name}</option>`;
        });
    }

    showEmployeeModal(employee = null) {
        const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
        const form = document.getElementById('employeeForm');
        const title = document.getElementById('employeeModalTitle');
        
        if (employee) {
            title.textContent = 'تعديل بيانات الموظف';
            this.populateForm(form, employee);
            this.selectedEmployee = employee;
        } else {
            title.textContent = 'إضافة موظف جديد';
            form.reset();
            document.querySelector('input[name="employee_number"]').value = Database.generateEmployeeNumber();
            document.querySelector('input[name="hire_date"]').value = new Date().toISOString().split('T')[0];
            document.querySelector('select[name="status"]').value = 'active';
            this.selectedEmployee = null;
        }
        
        this.loadModalOptions();
        modal.show();
    }

    populateForm(form, employee) {
        Object.keys(employee).forEach(key => {
            if (key === 'working_days') {
                // Handle working days checkboxes
                const workingDays = employee[key] || ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
                form.querySelectorAll('input[name="working_days"]').forEach(checkbox => {
                    checkbox.checked = workingDays.includes(checkbox.value);
                });
            } else {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    if (input.type === 'file') {
                        // Handle file inputs separately
                        if (employee[key]) {
                            this.showPhotoPreview(employee[key]);
                        }
                    } else {
                        input.value = employee[key] || '';
                    }
                }
            }
        });
    }

    previewPhoto(file) {
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.showPhotoPreview(e.target.result);
            };
            reader.readAsDataURL(file);
        }
    }

    showPhotoPreview(src) {
        const preview = document.getElementById('photoPreview');
        const img = document.getElementById('previewImage');
        img.src = src;
        preview.style.display = 'block';
    }

    saveEmployee() {
        const form = document.getElementById('employeeForm');
        const formData = new FormData(form);
        const employeeData = {};

        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            if (key !== 'photo' && key !== 'working_days') {
                employeeData[key] = value;
            }
        }

        // Handle working days checkboxes
        const workingDaysCheckboxes = form.querySelectorAll('input[name="working_days"]:checked');
        employeeData.working_days = Array.from(workingDaysCheckboxes).map(cb => cb.value);
        
        // Handle photo
        const photoFile = form.querySelector('input[name="photo"]').files[0];
        if (photoFile) {
            const reader = new FileReader();
            reader.onload = (e) => {
                employeeData.photo = e.target.result;
                this.saveEmployeeData(employeeData);
            };
            reader.readAsDataURL(photoFile);
        } else {
            if (this.selectedEmployee && this.selectedEmployee.photo) {
                employeeData.photo = this.selectedEmployee.photo;
            }
            this.saveEmployeeData(employeeData);
        }
    }

    saveEmployeeData(employeeData) {
        try {
            if (this.selectedEmployee) {
                // Update existing employee
                Database.updateEmployee(this.selectedEmployee.id, employeeData);
                window.samApp.showAlert('تم تحديث بيانات الموظف بنجاح', 'success');
            } else {
                // Create new employee
                Database.createEmployee(employeeData);
                window.samApp.showAlert('تم إضافة الموظف بنجاح', 'success');
            }
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('employeeModal'));
            modal.hide();
            this.loadEmployeesList();
            
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    viewEmployee(employeeId) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) {
            window.samApp.showAlert('الموظف غير موجود', 'danger');
            return;
        }
        
        this.selectedEmployee = employee;
        this.showEmployeeDetails(employee);
    }

    showEmployeeDetails(employee) {
        const modal = new bootstrap.Modal(document.getElementById('employeeDetailsModal'));
        const content = document.getElementById('employeeDetailsContent');
        
        const departments = Database.getAll('departments');
        const positions = Database.getAll('positions');
        
        const department = departments.find(d => d.id === employee.department);
        const position = positions.find(p => p.id === employee.position);
        
        content.innerHTML = `
            <div class="row">
                <div class="col-md-4 text-center">
                    <img src="${employee.photo || 'https://via.placeholder.com/200x200/007bff/ffffff?text=صورة'}" 
                         alt="${employee.name}" class="img-fluid rounded-circle mb-3" style="max-width: 200px;">
                    <h4>${employee.name}</h4>
                    <p class="text-muted">رقم وظيفي: ${employee.employee_number}</p>
                    ${this.getStatusBadge(employee.status)}
                </div>
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>رقم الهوية:</strong><br>
                            ${employee.national_id || 'غير محدد'}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>البريد الإلكتروني:</strong><br>
                            ${employee.email || 'غير محدد'}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>رقم الهاتف:</strong><br>
                            ${employee.phone || 'غير محدد'}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>تاريخ الميلاد:</strong><br>
                            ${employee.birth_date ? window.samApp.formatDate(employee.birth_date) : 'غير محدد'}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>القسم:</strong><br>
                            ${department ? department.name : 'غير محدد'}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>المنصب:</strong><br>
                            ${position ? position.name : 'غير محدد'}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>تاريخ التعيين:</strong><br>
                            ${employee.hire_date ? window.samApp.formatDate(employee.hire_date) : 'غير محدد'}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>الراتب الأساسي:</strong><br>
                            ${employee.salary ? window.samApp.formatCurrency(employee.salary) : 'غير محدد'}
                        </div>
                        <div class="col-12 mb-3">
                            <strong>العنوان:</strong><br>
                            ${employee.address || 'غير محدد'}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        modal.show();
    }

    deleteEmployee(employeeId) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) {
            window.samApp.showAlert('الموظف غير موجود', 'danger');
            return;
        }
        
        if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            try {
                Database.deleteEmployee(employeeId);
                window.samApp.showAlert('تم حذف الموظف بنجاح', 'success');
                this.loadEmployeesList();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    exportEmployees() {
        const employees = Database.getEmployees();
        const departments = Database.getAll('departments');
        const positions = Database.getAll('positions');
        
        // Prepare data for export
        const exportData = employees.map(emp => {
            const department = departments.find(d => d.id === emp.department);
            const position = positions.find(p => p.id === emp.position);
            
            return {
                'الرقم الوظيفي': emp.employee_number,
                'الاسم': emp.name,
                'رقم الهوية': emp.national_id,
                'البريد الإلكتروني': emp.email,
                'الهاتف': emp.phone,
                'القسم': department ? department.name : '',
                'المنصب': position ? position.name : '',
                'تاريخ التعيين': emp.hire_date,
                'الراتب': emp.salary,
                'الحالة': emp.status
            };
        });
        
        // Export to Excel
        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الموظفين');
        XLSX.writeFile(wb, `employees_${new Date().toISOString().split('T')[0]}.xlsx`);
        
        window.samApp.showAlert('تم تصدير بيانات الموظفين بنجاح', 'success');
    }

    printEmployees() {
        const employees = Database.getEmployees();
        const departments = Database.getAll('departments');
        const positions = Database.getAll('positions');

        // Prepare data for printing
        const employeesWithDetails = employees.map(emp => {
            const department = departments.find(d => d.id === emp.department);
            const position = positions.find(p => p.id === emp.position);

            return {
                ...emp,
                department: department ? department.name : 'غير محدد',
                position: position ? position.name : 'غير محدد'
            };
        });

        const filters = {
            department: this.filterDepartment,
            status: this.filterStatus,
            search: this.searchQuery
        };

        window.printManager.printEmployeesList(employeesWithDetails, filters);
    }
}
