/**
 * SAM - نظام إدارة شؤون الموظفين
 * Productivity Management Module
 * وحدة إدارة إنتاجية الموظفين والأقسام
 */

class ProductivityManager {
    constructor() {
        this.currentPeriod = 'month';
        this.selectedDepartment = '';
        this.selectedEmployee = '';
        this.chart = null;
    }

    render() {
        if (!window.authManager.hasPermission('reports')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadProductivityData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-chart-line me-2"></i>
                        إنتاجية الموظفين والأقسام
                    </h2>
                </div>
            </div>

            <!-- Productivity Overview -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="avgProductivity">0%</h4>
                                    <p class="mb-0">متوسط الإنتاجية</p>
                                </div>
                                <i class="fas fa-chart-line fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="topPerformer">-</h4>
                                    <p class="mb-0">أفضل موظف</p>
                                </div>
                                <i class="fas fa-trophy fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="topDepartment">-</h4>
                                    <p class="mb-0">أفضل قسم</p>
                                </div>
                                <i class="fas fa-building fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="improvementNeeded">0</h4>
                                    <p class="mb-0">يحتاج تحسين</p>
                                </div>
                                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Controls -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <label class="form-label">الفترة الزمنية</label>
                    <select class="form-select" id="periodFilter">
                        <option value="week">هذا الأسبوع</option>
                        <option value="month" selected>هذا الشهر</option>
                        <option value="quarter">هذا الربع</option>
                        <option value="year">هذا العام</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">القسم</label>
                    <select class="form-select" id="departmentFilter">
                        <option value="">جميع الأقسام</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الموظف</label>
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary flex-fill" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-success" id="exportProductivityBtn">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                    </div>
                </div>
            </div>

            <!-- Productivity Chart -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                مخطط الإنتاجية
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="productivityChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Productivity Tables -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2"></i>
                                إنتاجية الموظفين
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الموظف</th>
                                            <th>الحضور</th>
                                            <th>المهام</th>
                                            <th>الإنتاجية</th>
                                            <th>التقييم</th>
                                        </tr>
                                    </thead>
                                    <tbody id="employeeProductivityBody">
                                        <!-- Employee productivity will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-building me-2"></i>
                                إنتاجية الأقسام
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>القسم</th>
                                            <th>الموظفين</th>
                                            <th>متوسط الحضور</th>
                                            <th>الإنتاجية</th>
                                            <th>التقييم</th>
                                        </tr>
                                    </thead>
                                    <tbody id="departmentProductivityBody">
                                        <!-- Department productivity will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Productivity Details Modal -->
            <div class="modal fade" id="productivityDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل الإنتاجية</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="productivityDetailsContent">
                            <!-- Productivity details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="generateReportBtn">إنشاء تقرير</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Productivity Record Modal -->
            <div class="modal fade" id="addProductivityModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة سجل إنتاجية</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="productivityForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">التاريخ *</label>
                                        <input type="date" class="form-control" name="date" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">عدد المهام المكتملة</label>
                                        <input type="number" class="form-control" name="tasks_completed" min="0">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">عدد المهام المطلوبة</label>
                                        <input type="number" class="form-control" name="tasks_required" min="1">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">ساعات العمل الفعلية</label>
                                        <input type="number" class="form-control" name="hours_worked" step="0.5" min="0">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">ساعات العمل المطلوبة</label>
                                        <input type="number" class="form-control" name="hours_required" step="0.5" min="1">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تقييم الجودة (1-10)</label>
                                        <input type="number" class="form-control" name="quality_score" min="1" max="10">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نسبة الإنتاجية (%)</label>
                                        <input type="number" class="form-control" name="productivity_percentage" min="0" max="200" readonly>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="saveProductivityBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Floating Action Button -->
            <div class="position-fixed bottom-0 end-0 p-3">
                <button class="btn btn-primary btn-lg rounded-circle" id="addProductivityBtn" title="إضافة سجل إنتاجية">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
        `;
    }

    bindEvents() {
        // Period filter
        document.getElementById('periodFilter').addEventListener('change', (e) => {
            this.currentPeriod = e.target.value;
            this.loadProductivityData();
        });

        // Department filter
        document.getElementById('departmentFilter').addEventListener('change', (e) => {
            this.selectedDepartment = e.target.value;
            this.loadEmployeeOptions();
            this.loadProductivityData();
        });

        // Employee filter
        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadProductivityData();
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadProductivityData();
        });

        // Export button
        document.getElementById('exportProductivityBtn').addEventListener('click', () => {
            this.exportProductivity();
        });

        // Add productivity button
        document.getElementById('addProductivityBtn').addEventListener('click', () => {
            this.showAddProductivityModal();
        });

        // Save productivity
        document.getElementById('saveProductivityBtn').addEventListener('click', () => {
            this.saveProductivity();
        });

        // Auto-calculate productivity percentage
        const form = document.getElementById('productivityForm');
        ['tasks_completed', 'tasks_required', 'hours_worked', 'hours_required'].forEach(field => {
            form.querySelector(`input[name="${field}"]`).addEventListener('input', () => {
                this.calculateProductivityPercentage();
            });
        });
    }

    loadProductivityData() {
        this.loadDepartmentOptions();
        this.loadEmployeeOptions();
        this.loadProductivityStats();
        this.loadEmployeeProductivity();
        this.loadDepartmentProductivity();
        this.loadProductivityChart();
    }

    loadDepartmentOptions() {
        const departments = Database.getAll('departments');
        const select = document.getElementById('departmentFilter');

        select.innerHTML = '<option value="">جميع الأقسام</option>';
        departments.forEach(dept => {
            select.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
        });
    }

    loadEmployeeOptions() {
        let employees = Database.getEmployees().filter(emp => emp.status === 'active');

        if (this.selectedDepartment) {
            employees = employees.filter(emp => emp.department === this.selectedDepartment);
        }

        const selects = document.querySelectorAll('#employeeFilter, select[name="employee_id"]');
        selects.forEach(select => {
            const isFilter = select.id === 'employeeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الموظفين</option>' : '<option value="">اختر الموظف</option>';

            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadProductivityStats() {
        const productivity = this.getProductivityData();
        const employees = Database.getEmployees();
        const departments = Database.getAll('departments');

        // Calculate average productivity
        const avgProductivity = productivity.length > 0 ?
            productivity.reduce((sum, p) => sum + (p.productivity_percentage || 0), 0) / productivity.length : 0;

        // Find top performer
        const topPerformer = productivity.reduce((top, current) => {
            return (current.productivity_percentage || 0) > (top.productivity_percentage || 0) ? current : top;
        }, { productivity_percentage: 0 });

        const topEmployee = employees.find(emp => emp.id === topPerformer.employee_id);

        // Calculate department productivity
        const deptProductivity = this.calculateDepartmentProductivity();
        const topDept = deptProductivity.reduce((top, current) => {
            return current.productivity > top.productivity ? current : top;
        }, { productivity: 0 });

        // Count employees needing improvement (< 70%)
        const improvementNeeded = productivity.filter(p => (p.productivity_percentage || 0) < 70).length;

        // Update UI
        document.getElementById('avgProductivity').textContent = avgProductivity.toFixed(1) + '%';
        document.getElementById('topPerformer').textContent = topEmployee?.name || '-';
        document.getElementById('topDepartment').textContent = topDept.name || '-';
        document.getElementById('improvementNeeded').textContent = improvementNeeded;
    }

    getProductivityData() {
        const productivity = Database.getAll('productivity') || [];
        const dateRange = this.getDateRange();

        return productivity.filter(p => {
            const recordDate = new Date(p.date);
            return recordDate >= dateRange.start && recordDate <= dateRange.end;
        });
    }

    getDateRange() {
        const now = new Date();
        let start, end;

        switch (this.currentPeriod) {
            case 'week':
                start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
                end = new Date(start);
                end.setDate(start.getDate() + 6);
                break;
            case 'month':
                start = new Date(now.getFullYear(), now.getMonth(), 1);
                end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                break;
            case 'quarter':
                const quarter = Math.floor(now.getMonth() / 3);
                start = new Date(now.getFullYear(), quarter * 3, 1);
                end = new Date(now.getFullYear(), quarter * 3 + 3, 0);
                break;
            case 'year':
                start = new Date(now.getFullYear(), 0, 1);
                end = new Date(now.getFullYear(), 11, 31);
                break;
            default:
                start = new Date(now.getFullYear(), now.getMonth(), 1);
                end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        }

        return { start, end };
    }

    loadEmployeeProductivity() {
        const productivity = this.getProductivityData();
        const employees = Database.getEmployees();
        const attendance = Database.getAll('attendance');
        const dateRange = this.getDateRange();

        // Group productivity by employee
        const employeeStats = {};

        employees.forEach(emp => {
            if (this.selectedEmployee && emp.id !== this.selectedEmployee) return;
            if (this.selectedDepartment && emp.department !== this.selectedDepartment) return;

            const empProductivity = productivity.filter(p => p.employee_id === emp.id);
            const empAttendance = attendance.filter(a => {
                const recordDate = new Date(a.date);
                return a.employee_id === emp.id && recordDate >= dateRange.start && recordDate <= dateRange.end;
            });

            const avgProductivity = empProductivity.length > 0 ?
                empProductivity.reduce((sum, p) => sum + (p.productivity_percentage || 0), 0) / empProductivity.length : 0;

            const attendanceRate = empAttendance.length > 0 ?
                (empAttendance.filter(a => a.status !== 'absent').length / empAttendance.length) * 100 : 0;

            const totalTasks = empProductivity.reduce((sum, p) => sum + (p.tasks_completed || 0), 0);

            employeeStats[emp.id] = {
                employee: emp,
                productivity: avgProductivity,
                attendanceRate: attendanceRate,
                totalTasks: totalTasks,
                records: empProductivity.length
            };
        });

        this.renderEmployeeProductivity(Object.values(employeeStats));
    }

    renderEmployeeProductivity(employeeStats) {
        const tbody = document.getElementById('employeeProductivityBody');

        if (employeeStats.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد بيانات إنتاجية</p>
                    </td>
                </tr>
            `;
            return;
        }

        // Sort by productivity
        employeeStats.sort((a, b) => b.productivity - a.productivity);

        tbody.innerHTML = employeeStats.map(stat => {
            const productivityBadge = this.getProductivityBadge(stat.productivity);
            const attendanceBadge = this.getAttendanceBadge(stat.attendanceRate);

            return `
                <tr class="cursor-pointer" onclick="productivityManager.showEmployeeDetails('${stat.employee.id}')">
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${stat.employee.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}"
                                 alt="${stat.employee.name}" class="rounded-circle me-2" width="40" height="40">
                            <div>
                                <div class="fw-bold">${stat.employee.name}</div>
                                <small class="text-muted">${stat.employee.employee_number}</small>
                            </div>
                        </div>
                    </td>
                    <td>${attendanceBadge}</td>
                    <td><span class="badge bg-info">${stat.totalTasks}</span></td>
                    <td><strong>${stat.productivity.toFixed(1)}%</strong></td>
                    <td>${productivityBadge}</td>
                </tr>
            `;
        }).join('');
    }

    calculateDepartmentProductivity() {
        const productivity = this.getProductivityData();
        const employees = Database.getEmployees();
        const departments = Database.getAll('departments');
        const attendance = Database.getAll('attendance');
        const dateRange = this.getDateRange();

        return departments.map(dept => {
            const deptEmployees = employees.filter(emp => emp.department === dept.id);
            const deptProductivity = productivity.filter(p =>
                deptEmployees.some(emp => emp.id === p.employee_id)
            );
            const deptAttendance = attendance.filter(a => {
                const recordDate = new Date(a.date);
                return deptEmployees.some(emp => emp.id === a.employee_id) &&
                       recordDate >= dateRange.start && recordDate <= dateRange.end;
            });

            const avgProductivity = deptProductivity.length > 0 ?
                deptProductivity.reduce((sum, p) => sum + (p.productivity_percentage || 0), 0) / deptProductivity.length : 0;

            const attendanceRate = deptAttendance.length > 0 ?
                (deptAttendance.filter(a => a.status !== 'absent').length / deptAttendance.length) * 100 : 0;

            return {
                id: dept.id,
                name: dept.name,
                employeeCount: deptEmployees.length,
                productivity: avgProductivity,
                attendanceRate: attendanceRate
            };
        });
    }

    loadDepartmentProductivity() {
        const deptStats = this.calculateDepartmentProductivity();
        this.renderDepartmentProductivity(deptStats);
    }

    renderDepartmentProductivity(deptStats) {
        const tbody = document.getElementById('departmentProductivityBody');

        if (deptStats.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <i class="fas fa-building fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد أقسام</p>
                    </td>
                </tr>
            `;
            return;
        }

        // Sort by productivity
        deptStats.sort((a, b) => b.productivity - a.productivity);

        tbody.innerHTML = deptStats.map(dept => {
            const productivityBadge = this.getProductivityBadge(dept.productivity);
            const attendanceBadge = this.getAttendanceBadge(dept.attendanceRate);

            return `
                <tr class="cursor-pointer" onclick="productivityManager.showDepartmentDetails('${dept.id}')">
                    <td>
                        <div class="fw-bold">${dept.name}</div>
                    </td>
                    <td><span class="badge bg-secondary">${dept.employeeCount}</span></td>
                    <td>${attendanceBadge}</td>
                    <td><strong>${dept.productivity.toFixed(1)}%</strong></td>
                    <td>${productivityBadge}</td>
                </tr>
            `;
        }).join('');
    }

    getProductivityBadge(productivity) {
        if (productivity >= 90) return '<span class="badge bg-success">ممتاز</span>';
        if (productivity >= 80) return '<span class="badge bg-primary">جيد جداً</span>';
        if (productivity >= 70) return '<span class="badge bg-info">جيد</span>';
        if (productivity >= 60) return '<span class="badge bg-warning">مقبول</span>';
        return '<span class="badge bg-danger">ضعيف</span>';
    }

    getAttendanceBadge(attendanceRate) {
        const rate = attendanceRate.toFixed(1);
        if (attendanceRate >= 95) return `<span class="badge bg-success">${rate}%</span>`;
        if (attendanceRate >= 85) return `<span class="badge bg-primary">${rate}%</span>`;
        if (attendanceRate >= 75) return `<span class="badge bg-warning">${rate}%</span>`;
        return `<span class="badge bg-danger">${rate}%</span>`;
    }

    loadProductivityChart() {
        const ctx = document.getElementById('productivityChart').getContext('2d');
        const productivity = this.getProductivityData();

        // Destroy existing chart
        if (this.chart) {
            this.chart.destroy();
        }

        // Group data by date
        const dateGroups = {};
        productivity.forEach(p => {
            const date = p.date;
            if (!dateGroups[date]) {
                dateGroups[date] = [];
            }
            dateGroups[date].push(p.productivity_percentage || 0);
        });

        // Calculate daily averages
        const labels = Object.keys(dateGroups).sort();
        const data = labels.map(date => {
            const dayData = dateGroups[date];
            return dayData.reduce((sum, val) => sum + val, 0) / dayData.length;
        });

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels.map(date => window.samApp.formatDate(date)),
                datasets: [{
                    label: 'متوسط الإنتاجية اليومية',
                    data: data,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    }

    showAddProductivityModal() {
        const modal = new bootstrap.Modal(document.getElementById('addProductivityModal'));
        const form = document.getElementById('productivityForm');

        form.reset();
        form.querySelector('input[name="date"]').value = new Date().toISOString().split('T')[0];

        modal.show();
    }

    calculateProductivityPercentage() {
        const form = document.getElementById('productivityForm');
        const tasksCompleted = parseFloat(form.querySelector('input[name="tasks_completed"]').value) || 0;
        const tasksRequired = parseFloat(form.querySelector('input[name="tasks_required"]').value) || 1;
        const hoursWorked = parseFloat(form.querySelector('input[name="hours_worked"]').value) || 0;
        const hoursRequired = parseFloat(form.querySelector('input[name="hours_required"]').value) || 1;

        // Calculate productivity based on tasks and time efficiency
        const taskEfficiency = (tasksCompleted / tasksRequired) * 100;
        const timeEfficiency = (hoursRequired / hoursWorked) * 100;

        // Average of both efficiencies
        const productivity = (taskEfficiency + timeEfficiency) / 2;

        form.querySelector('input[name="productivity_percentage"]').value = Math.min(200, productivity).toFixed(1);
    }

    saveProductivity() {
        const form = document.getElementById('productivityForm');
        const formData = new FormData(form);
        const productivityData = Object.fromEntries(formData.entries());

        // Convert numeric fields
        const numericFields = ['tasks_completed', 'tasks_required', 'hours_worked', 'hours_required', 'quality_score', 'productivity_percentage'];
        numericFields.forEach(field => {
            if (productivityData[field]) {
                productivityData[field] = parseFloat(productivityData[field]) || 0;
            }
        });

        try {
            Database.create('productivity', productivityData);
            window.samApp.showAlert('تم حفظ سجل الإنتاجية بنجاح', 'success');

            const modal = bootstrap.Modal.getInstance(document.getElementById('addProductivityModal'));
            modal.hide();
            this.loadProductivityData();

        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    showEmployeeDetails(employeeId) {
        const employee = Database.getEmployee(employeeId);
        const productivity = this.getProductivityData().filter(p => p.employee_id === employeeId);

        if (!employee) return;

        this.showProductivityDetails('employee', employee, productivity);
    }

    showDepartmentDetails(departmentId) {
        const department = Database.getAll('departments').find(d => d.id === departmentId);
        const employees = Database.getEmployees().filter(emp => emp.department === departmentId);
        const productivity = this.getProductivityData().filter(p =>
            employees.some(emp => emp.id === p.employee_id)
        );

        if (!department) return;

        this.showProductivityDetails('department', department, productivity, employees);
    }

    showProductivityDetails(type, entity, productivity, employees = null) {
        const modal = new bootstrap.Modal(document.getElementById('productivityDetailsModal'));
        const content = document.getElementById('productivityDetailsContent');

        if (type === 'employee') {
            content.innerHTML = this.getEmployeeDetailsHTML(entity, productivity);
        } else {
            content.innerHTML = this.getDepartmentDetailsHTML(entity, productivity, employees);
        }

        modal.show();
    }

    getEmployeeDetailsHTML(employee, productivity) {
        const avgProductivity = productivity.length > 0 ?
            productivity.reduce((sum, p) => sum + (p.productivity_percentage || 0), 0) / productivity.length : 0;

        const totalTasks = productivity.reduce((sum, p) => sum + (p.tasks_completed || 0), 0);
        const avgQuality = productivity.length > 0 ?
            productivity.reduce((sum, p) => sum + (p.quality_score || 0), 0) / productivity.length : 0;

        return `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات الموظف</h6>
                    <p><strong>الاسم:</strong> ${employee.name}</p>
                    <p><strong>الرقم الوظيفي:</strong> ${employee.employee_number}</p>
                    <p><strong>القسم:</strong> ${employee.department}</p>
                    <p><strong>المنصب:</strong> ${employee.position}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">إحصائيات الإنتاجية</h6>
                    <p><strong>متوسط الإنتاجية:</strong> ${avgProductivity.toFixed(1)}%</p>
                    <p><strong>إجمالي المهام:</strong> ${totalTasks}</p>
                    <p><strong>متوسط الجودة:</strong> ${avgQuality.toFixed(1)}/10</p>
                    <p><strong>عدد السجلات:</strong> ${productivity.length}</p>
                </div>
                <div class="col-12">
                    <h6 class="text-primary">سجل الإنتاجية التفصيلي</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المهام</th>
                                    <th>الساعات</th>
                                    <th>الجودة</th>
                                    <th>الإنتاجية</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${productivity.map(p => `
                                    <tr>
                                        <td>${window.samApp.formatDate(p.date)}</td>
                                        <td>${p.tasks_completed}/${p.tasks_required}</td>
                                        <td>${p.hours_worked}/${p.hours_required}</td>
                                        <td>${p.quality_score}/10</td>
                                        <td>${p.productivity_percentage.toFixed(1)}%</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    getDepartmentDetailsHTML(department, productivity, employees) {
        const avgProductivity = productivity.length > 0 ?
            productivity.reduce((sum, p) => sum + (p.productivity_percentage || 0), 0) / productivity.length : 0;

        return `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات القسم</h6>
                    <p><strong>اسم القسم:</strong> ${department.name}</p>
                    <p><strong>عدد الموظفين:</strong> ${employees.length}</p>
                    <p><strong>متوسط الإنتاجية:</strong> ${avgProductivity.toFixed(1)}%</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">أداء الموظفين</h6>
                    ${employees.map(emp => {
                        const empProductivity = productivity.filter(p => p.employee_id === emp.id);
                        const empAvg = empProductivity.length > 0 ?
                            empProductivity.reduce((sum, p) => sum + (p.productivity_percentage || 0), 0) / empProductivity.length : 0;
                        return `<p><strong>${emp.name}:</strong> ${empAvg.toFixed(1)}%</p>`;
                    }).join('')}
                </div>
            </div>
        `;
    }

    exportProductivity() {
        const productivity = this.getProductivityData();
        const employees = Database.getEmployees();

        const exportData = productivity.map(p => {
            const employee = employees.find(emp => emp.id === p.employee_id);
            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'التاريخ': p.date,
                'المهام المكتملة': p.tasks_completed,
                'المهام المطلوبة': p.tasks_required,
                'الساعات المعملة': p.hours_worked,
                'الساعات المطلوبة': p.hours_required,
                'تقييم الجودة': p.quality_score,
                'نسبة الإنتاجية': p.productivity_percentage,
                'ملاحظات': p.notes || ''
            };
        });

        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الإنتاجية');
        XLSX.writeFile(wb, `productivity_${this.currentPeriod}_${new Date().toISOString().split('T')[0]}.xlsx`);

        window.samApp.showAlert('تم تصدير بيانات الإنتاجية بنجاح', 'success');
    }
}

// Global reference
let productivityManager;
