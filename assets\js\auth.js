/**
 * SAM - نظام إدارة شؤون الموظفين
 * Authentication Management
 * إدارة المصادقة والصلاحيات
 */

class AuthManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindLoginForm();
        this.initializeDefaultUsers();
    }

    bindLoginForm() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }
    }

    initializeDefaultUsers() {
        // Initialize default users if not exists
        const users = this.getUsers();
        if (users.length === 0) {
            const defaultUsers = [
                {
                    id: 'admin',
                    username: 'admin',
                    password: 'admin123',
                    name: 'المدير العام',
                    role: 'admin',
                    permissions: ['all'],
                    email: '<EMAIL>',
                    phone: '+963998171954',
                    created_at: new Date().toISOString(),
                    last_login: null,
                    active: true
                },
                {
                    id: 'hr',
                    username: 'hr',
                    password: 'hr123',
                    name: 'مدير الموارد البشرية',
                    role: 'hr_manager',
                    permissions: ['employees', 'attendance', 'leaves', 'payroll', 'reports'],
                    email: '<EMAIL>',
                    phone: '+************',
                    created_at: new Date().toISOString(),
                    last_login: null,
                    active: true
                },
                {
                    id: 'accountant',
                    username: 'accountant',
                    password: 'acc123',
                    name: 'المحاسب',
                    role: 'accountant',
                    permissions: ['payroll', 'reports'],
                    email: '<EMAIL>',
                    phone: '+************',
                    created_at: new Date().toISOString(),
                    last_login: null,
                    active: true
                }
            ];

            localStorage.setItem('sam_users', JSON.stringify(defaultUsers));
        }
    }

    handleLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        if (!username || !password) {
            this.showLoginError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        const user = this.authenticateUser(username, password);
        
        if (user) {
            if (!user.active) {
                this.showLoginError('هذا الحساب غير مفعل. يرجى الاتصال بالمدير');
                return;
            }

            // Update last login
            user.last_login = new Date().toISOString();
            this.updateUser(user);

            // Save current user
            if (rememberMe) {
                localStorage.setItem('sam_current_user', JSON.stringify(user));
            } else {
                sessionStorage.setItem('sam_current_user', JSON.stringify(user));
            }

            // Clear form
            document.getElementById('loginForm').reset();
            
            // Show success message
            this.showLoginSuccess('تم تسجيل الدخول بنجاح');
            
            // Redirect to main app
            setTimeout(() => {
                window.samApp.currentUser = user;
                window.samApp.showMainApp();
                window.samApp.updateUserDisplay();
            }, 1000);

        } else {
            this.showLoginError('اسم المستخدم أو كلمة المرور غير صحيحة');
        }
    }

    authenticateUser(username, password) {
        const users = this.getUsers();
        return users.find(user => 
            user.username === username && user.password === password
        );
    }

    getUsers() {
        const users = localStorage.getItem('sam_users');
        return users ? JSON.parse(users) : [];
    }

    updateUser(updatedUser) {
        const users = this.getUsers();
        const index = users.findIndex(user => user.id === updatedUser.id);
        if (index !== -1) {
            users[index] = updatedUser;
            localStorage.setItem('sam_users', JSON.stringify(users));
        }
    }

    getCurrentUser() {
        let user = localStorage.getItem('sam_current_user');
        if (!user) {
            user = sessionStorage.getItem('sam_current_user');
        }
        return user ? JSON.parse(user) : null;
    }

    hasPermission(permission) {
        const user = this.getCurrentUser();
        if (!user) return false;
        
        // Admin has all permissions
        if (user.role === 'admin' || user.permissions.includes('all')) {
            return true;
        }
        
        return user.permissions.includes(permission);
    }

    checkPermission(permission) {
        if (!this.hasPermission(permission)) {
            this.showPermissionError();
            return false;
        }
        return true;
    }

    showLoginError(message) {
        this.removeExistingAlerts();
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const loginForm = document.getElementById('loginForm');
        loginForm.appendChild(alertDiv);
    }

    showLoginSuccess(message) {
        this.removeExistingAlerts();
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const loginForm = document.getElementById('loginForm');
        loginForm.appendChild(alertDiv);
    }

    showPermissionError() {
        if (window.samApp) {
            window.samApp.showAlert('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger');
        }
    }

    removeExistingAlerts() {
        const existingAlerts = document.querySelectorAll('#loginForm .alert');
        existingAlerts.forEach(alert => alert.remove());
    }

    // User Management Methods
    createUser(userData) {
        if (!this.hasPermission('users')) {
            this.showPermissionError();
            return false;
        }

        const users = this.getUsers();
        
        // Check if username already exists
        if (users.find(user => user.username === userData.username)) {
            throw new Error('اسم المستخدم موجود بالفعل');
        }

        const newUser = {
            id: this.generateUserId(),
            username: userData.username,
            password: userData.password,
            name: userData.name,
            role: userData.role,
            permissions: userData.permissions || [],
            email: userData.email || '',
            phone: userData.phone || '',
            created_at: new Date().toISOString(),
            last_login: null,
            active: true
        };

        users.push(newUser);
        localStorage.setItem('sam_users', JSON.stringify(users));
        return newUser;
    }

    updateUserData(userId, userData) {
        if (!this.hasPermission('users')) {
            this.showPermissionError();
            return false;
        }

        const users = this.getUsers();
        const index = users.findIndex(user => user.id === userId);
        
        if (index === -1) {
            throw new Error('المستخدم غير موجود');
        }

        // Don't allow updating username if it conflicts with existing user
        if (userData.username && userData.username !== users[index].username) {
            if (users.find(user => user.username === userData.username && user.id !== userId)) {
                throw new Error('اسم المستخدم موجود بالفعل');
            }
        }

        users[index] = { ...users[index], ...userData };
        localStorage.setItem('sam_users', JSON.stringify(users));
        return users[index];
    }

    deleteUser(userId) {
        if (!this.hasPermission('users')) {
            this.showPermissionError();
            return false;
        }

        const users = this.getUsers();
        const filteredUsers = users.filter(user => user.id !== userId);
        
        if (filteredUsers.length === users.length) {
            throw new Error('المستخدم غير موجود');
        }

        localStorage.setItem('sam_users', JSON.stringify(filteredUsers));
        return true;
    }

    toggleUserStatus(userId) {
        if (!this.hasPermission('users')) {
            this.showPermissionError();
            return false;
        }

        const users = this.getUsers();
        const user = users.find(user => user.id === userId);
        
        if (!user) {
            throw new Error('المستخدم غير موجود');
        }

        user.active = !user.active;
        localStorage.setItem('sam_users', JSON.stringify(users));
        return user;
    }

    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Role and Permission Management
    getRoles() {
        return [
            {
                id: 'admin',
                name: 'مدير النظام',
                permissions: ['all']
            },
            {
                id: 'hr_manager',
                name: 'مدير الموارد البشرية',
                permissions: ['employees', 'attendance', 'leaves', 'payroll', 'reports']
            },
            {
                id: 'accountant',
                name: 'محاسب',
                permissions: ['payroll', 'reports']
            },
            {
                id: 'employee',
                name: 'موظف',
                permissions: ['attendance', 'leaves']
            }
        ];
    }

    getPermissions() {
        return [
            { id: 'employees', name: 'إدارة الموظفين' },
            { id: 'attendance', name: 'إدارة الدوام' },
            { id: 'leaves', name: 'إدارة الإجازات' },
            { id: 'payroll', name: 'إدارة الرواتب' },
            { id: 'reports', name: 'التقارير' },
            { id: 'users', name: 'إدارة المستخدمين' },
            { id: 'backup', name: 'النسخ الاحتياطي' },
            { id: 'all', name: 'جميع الصلاحيات' }
        ];
    }

    logout() {
        localStorage.removeItem('sam_current_user');
        sessionStorage.removeItem('sam_current_user');
        
        if (window.samApp) {
            window.samApp.currentUser = null;
            window.samApp.showLoginScreen();
        }
    }
}

// Initialize Auth Manager
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});
