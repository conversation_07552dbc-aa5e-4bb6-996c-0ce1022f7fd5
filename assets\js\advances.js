/**
 * SAM - نظام إدارة شؤون الموظفين
 * Advances Management Module
 * وحدة إدارة السلف
 */

class AdvancesManager {
    constructor() {
        this.currentView = 'all';
        this.selectedEmployee = '';
        this.selectedStatus = '';
    }

    render() {
        if (!window.authManager.hasPermission('payroll')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadAdvancesData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-hand-holding-usd me-2"></i>
                            إدارة السلف
                        </h2>
                        <button class="btn btn-primary" id="addAdvanceBtn">
                            <i class="fas fa-plus me-2"></i>
                            إضافة سلفة جديدة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Advances Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalAdvances">0</h4>
                                    <p class="mb-0">إجمالي السلف</p>
                                </div>
                                <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="activeAdvances">0</h4>
                                    <p class="mb-0">سلف نشطة</p>
                                </div>
                                <i class="fas fa-hourglass-half fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="paidAdvances">0</h4>
                                    <p class="mb-0">سلف مسددة</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="remainingAmount">0</h4>
                                    <p class="mb-0">المبلغ المتبقي</p>
                                </div>
                                <i class="fas fa-calculator fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشطة</option>
                        <option value="completed">مكتملة</option>
                        <option value="cancelled">ملغية</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="typeFilter">
                        <option value="">جميع الأنواع</option>
                        <option value="salary">سلفة راتب</option>
                        <option value="emergency">سلفة طارئة</option>
                        <option value="personal">سلفة شخصية</option>
                        <option value="medical">سلفة طبية</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-success w-100" id="exportAdvancesBtn">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <!-- Advances Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        سجل السلف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع السلفة</th>
                                    <th>المبلغ</th>
                                    <th>عدد الأقساط</th>
                                    <th>القسط الشهري</th>
                                    <th>المدفوع</th>
                                    <th>المتبقي</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="advancesTableBody">
                                <!-- Advance records will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Advance Modal -->
            <div class="modal fade" id="advanceModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="advanceModalTitle">إضافة سلفة جديدة</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="advanceForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع السلفة *</label>
                                        <select class="form-select" name="advance_type" required>
                                            <option value="">اختر نوع السلفة</option>
                                            <option value="salary">سلفة راتب</option>
                                            <option value="emergency">سلفة طارئة</option>
                                            <option value="personal">سلفة شخصية</option>
                                            <option value="medical">سلفة طبية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">مبلغ السلفة *</label>
                                        <input type="number" class="form-control" name="amount" step="0.01" min="0" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">عدد الأقساط *</label>
                                        <input type="number" class="form-control" name="installments" min="1" max="24" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">القسط الشهري</label>
                                        <input type="number" class="form-control" name="monthly_installment" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ البداية</label>
                                        <input type="date" class="form-control" name="start_date">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">معدل الفائدة (%)</label>
                                        <input type="number" class="form-control" name="interest_rate" step="0.01" min="0" max="100" value="0">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="active">نشطة</option>
                                            <option value="completed">مكتملة</option>
                                            <option value="cancelled">ملغية</option>
                                        </select>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">سبب السلفة *</label>
                                        <textarea class="form-control" name="reason" rows="3" required></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="2"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="calculateInstallmentBtn">حساب القسط</button>
                            <button type="button" class="btn btn-success" id="saveAdvanceBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advance Details Modal -->
            <div class="modal fade" id="advanceDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل السلفة</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="advanceDetailsContent">
                            <!-- Advance details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="editAdvanceBtn">تعديل</button>
                            <button type="button" class="btn btn-success" id="payInstallmentBtn">دفع قسط</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Modal -->
            <div class="modal fade" id="paymentModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">دفع قسط</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="paymentForm">
                                <div class="mb-3">
                                    <label class="form-label">مبلغ القسط</label>
                                    <input type="number" class="form-control" name="payment_amount" step="0.01" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الدفع</label>
                                    <input type="date" class="form-control" name="payment_date" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">طريقة الدفع</label>
                                    <select class="form-select" name="payment_method">
                                        <option value="salary_deduction">خصم من الراتب</option>
                                        <option value="cash">نقداً</option>
                                        <option value="bank_transfer">تحويل بنكي</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="payment_notes" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-success" id="savePaymentBtn">حفظ الدفع</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Add advance button
        document.getElementById('addAdvanceBtn').addEventListener('click', () => {
            this.showAdvanceModal();
        });

        // Filter events
        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadAdvancesData();
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.selectedStatus = e.target.value;
            this.loadAdvancesData();
        });

        // Export button
        document.getElementById('exportAdvancesBtn').addEventListener('click', () => {
            this.exportAdvances();
        });

        // Save advance
        document.getElementById('saveAdvanceBtn').addEventListener('click', () => {
            this.saveAdvance();
        });

        // Calculate installment
        document.getElementById('calculateInstallmentBtn').addEventListener('click', () => {
            this.calculateInstallment();
        });

        // Save payment
        document.getElementById('savePaymentBtn').addEventListener('click', () => {
            this.savePayment();
        });

        // Auto-calculate installment when amount or installments change
        const amountInput = document.querySelector('input[name="amount"]');
        const installmentsInput = document.querySelector('input[name="installments"]');
        const interestInput = document.querySelector('input[name="interest_rate"]');

        [amountInput, installmentsInput, interestInput].forEach(input => {
            if (input) {
                input.addEventListener('input', () => {
                    this.calculateInstallment();
                });
            }
        });
    }

    loadAdvancesData() {
        this.loadEmployeeOptions();
        this.loadAdvancesTable();
        this.updateAdvanceStats();
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const selects = document.querySelectorAll('#employeeFilter, select[name="employee_id"]');

        selects.forEach(select => {
            const isFilter = select.id === 'employeeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الموظفين</option>' : '<option value="">اختر الموظف</option>';

            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadAdvancesTable() {
        let advances = Database.getAll('advances') || [];

        // Apply filters
        if (this.selectedEmployee) {
            advances = advances.filter(adv => adv.employee_id === this.selectedEmployee);
        }

        if (this.selectedStatus) {
            advances = advances.filter(adv => adv.status === this.selectedStatus);
        }

        // Sort by creation date (newest first)
        advances.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        this.renderAdvancesTable(advances);
    }

    renderAdvancesTable(advances) {
        const tbody = document.getElementById('advancesTableBody');
        const employees = Database.getEmployees();

        if (advances.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-4">
                        <i class="fas fa-hand-holding-usd fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد سلف</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = advances.map(advance => {
            const employee = employees.find(emp => emp.id === advance.employee_id);
            const statusBadge = this.getStatusBadge(advance.status);
            const paidAmount = this.calculatePaidAmount(advance);
            const remainingAmount = (advance.amount || 0) - paidAmount;

            return `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${employee?.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}"
                                 alt="${employee?.name}" class="rounded-circle me-2" width="40" height="40">
                            <div>
                                <div class="fw-bold">${employee?.name || 'غير معروف'}</div>
                                <small class="text-muted">${employee?.employee_number || ''}</small>
                            </div>
                        </div>
                    </td>
                    <td>${this.getAdvanceTypeText(advance.advance_type)}</td>
                    <td>${window.samApp.formatCurrency(advance.amount || 0)}</td>
                    <td>${advance.installments || 0}</td>
                    <td>${window.samApp.formatCurrency(advance.monthly_installment || 0)}</td>
                    <td>${window.samApp.formatCurrency(paidAmount)}</td>
                    <td>${window.samApp.formatCurrency(remainingAmount)}</td>
                    <td>${statusBadge}</td>
                    <td>${window.samApp.formatDate(advance.created_at)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary view-advance"
                                    data-advance-id="${advance.id}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success edit-advance"
                                    data-advance-id="${advance.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info pay-advance"
                                    data-advance-id="${advance.id}">
                                <i class="fas fa-money-bill"></i>
                            </button>
                            <button class="btn btn-outline-danger delete-advance"
                                    data-advance-id="${advance.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View advance
        document.querySelectorAll('.view-advance').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const advanceId = e.target.closest('.view-advance').dataset.advanceId;
                this.viewAdvance(advanceId);
            });
        });

        // Edit advance
        document.querySelectorAll('.edit-advance').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const advanceId = e.target.closest('.edit-advance').dataset.advanceId;
                const advance = Database.getById('advances', advanceId);
                this.showAdvanceModal(advance);
            });
        });

        // Pay advance
        document.querySelectorAll('.pay-advance').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const advanceId = e.target.closest('.pay-advance').dataset.advanceId;
                this.showPaymentModal(advanceId);
            });
        });

        // Delete advance
        document.querySelectorAll('.delete-advance').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const advanceId = e.target.closest('.delete-advance').dataset.advanceId;
                this.deleteAdvance(advanceId);
            });
        });
    }

    getStatusBadge(status) {
        const statusMap = {
            'active': { class: 'warning', text: 'نشطة' },
            'completed': { class: 'success', text: 'مكتملة' },
            'cancelled': { class: 'danger', text: 'ملغية' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    getAdvanceTypeText(type) {
        const typeMap = {
            'salary': 'سلفة راتب',
            'emergency': 'سلفة طارئة',
            'personal': 'سلفة شخصية',
            'medical': 'سلفة طبية'
        };
        return typeMap[type] || 'غير محدد';
    }

    calculatePaidAmount(advance) {
        const payments = Database.getAll('advance_payments') || [];
        return payments
            .filter(payment => payment.advance_id === advance.id)
            .reduce((sum, payment) => sum + (payment.payment_amount || 0), 0);
    }

    updateAdvanceStats() {
        const advances = Database.getAll('advances') || [];

        const totalAmount = advances.reduce((sum, adv) => sum + (adv.amount || 0), 0);
        const activeAdvances = advances.filter(adv => adv.status === 'active').length;
        const paidAdvances = advances.filter(adv => adv.status === 'completed').length;

        let remainingAmount = 0;
        advances.forEach(advance => {
            const paidAmount = this.calculatePaidAmount(advance);
            remainingAmount += (advance.amount || 0) - paidAmount;
        });

        document.getElementById('totalAdvances').textContent = window.samApp.formatCurrency(totalAmount);
        document.getElementById('activeAdvances').textContent = activeAdvances;
        document.getElementById('paidAdvances').textContent = paidAdvances;
        document.getElementById('remainingAmount').textContent = window.samApp.formatCurrency(remainingAmount);
    }

    showAdvanceModal(advance = null) {
        const modal = new bootstrap.Modal(document.getElementById('advanceModal'));
        const form = document.getElementById('advanceForm');
        const title = document.getElementById('advanceModalTitle');

        if (advance) {
            title.textContent = 'تعديل السلفة';
            this.populateAdvanceForm(form, advance);
            this.selectedAdvance = advance;
        } else {
            title.textContent = 'إضافة سلفة جديدة';
            form.reset();
            form.querySelector('input[name="start_date"]').value = new Date().toISOString().split('T')[0];
            form.querySelector('select[name="status"]').value = 'active';
            this.selectedAdvance = null;
        }

        modal.show();
    }

    populateAdvanceForm(form, advance) {
        Object.keys(advance).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = advance[key] || '';
            }
        });
    }

    calculateInstallment() {
        const amount = parseFloat(document.querySelector('input[name="amount"]').value) || 0;
        const installments = parseInt(document.querySelector('input[name="installments"]').value) || 1;
        const interestRate = parseFloat(document.querySelector('input[name="interest_rate"]').value) || 0;

        if (amount > 0 && installments > 0) {
            // Calculate with interest
            const totalAmount = amount * (1 + interestRate / 100);
            const monthlyInstallment = totalAmount / installments;

            document.querySelector('input[name="monthly_installment"]').value = monthlyInstallment.toFixed(2);
        }
    }

    saveAdvance() {
        const form = document.getElementById('advanceForm');
        const formData = new FormData(form);
        const advanceData = Object.fromEntries(formData.entries());

        // Convert numeric fields
        const numericFields = ['amount', 'installments', 'monthly_installment', 'interest_rate'];
        numericFields.forEach(field => {
            advanceData[field] = parseFloat(advanceData[field]) || 0;
        });

        try {
            if (this.selectedAdvance) {
                // Update existing advance
                Database.update('advances', this.selectedAdvance.id, advanceData);
                window.samApp.showAlert('تم تحديث السلفة بنجاح', 'success');
            } else {
                // Create new advance
                Database.create('advances', advanceData);
                window.samApp.showAlert('تم إضافة السلفة بنجاح', 'success');
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('advanceModal'));
            modal.hide();
            this.loadAdvancesData();

        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    viewAdvance(advanceId) {
        const advance = Database.getById('advances', advanceId);
        if (!advance) {
            window.samApp.showAlert('السلفة غير موجودة', 'danger');
            return;
        }

        this.showAdvanceDetails(advance);
    }

    showAdvanceDetails(advance) {
        const modal = new bootstrap.Modal(document.getElementById('advanceDetailsModal'));
        const content = document.getElementById('advanceDetailsContent');

        const employee = Database.getEmployee(advance.employee_id);
        const paidAmount = this.calculatePaidAmount(advance);
        const remainingAmount = (advance.amount || 0) - paidAmount;
        const payments = Database.getAll('advance_payments') || [];
        const advancePayments = payments.filter(p => p.advance_id === advance.id);

        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات الموظف</h6>
                    <p><strong>الاسم:</strong> ${employee?.name || 'غير معروف'}</p>
                    <p><strong>الرقم الوظيفي:</strong> ${employee?.employee_number || ''}</p>
                    <p><strong>القسم:</strong> ${employee?.department || 'غير محدد'}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">تفاصيل السلفة</h6>
                    <p><strong>نوع السلفة:</strong> ${this.getAdvanceTypeText(advance.advance_type)}</p>
                    <p><strong>المبلغ الإجمالي:</strong> ${window.samApp.formatCurrency(advance.amount || 0)}</p>
                    <p><strong>عدد الأقساط:</strong> ${advance.installments || 0}</p>
                    <p><strong>القسط الشهري:</strong> ${window.samApp.formatCurrency(advance.monthly_installment || 0)}</p>
                    <p><strong>معدل الفائدة:</strong> ${advance.interest_rate || 0}%</p>
                    <p><strong>تاريخ البداية:</strong> ${window.samApp.formatDate(advance.start_date)}</p>
                    <p><strong>الحالة:</strong> ${this.getStatusBadge(advance.status)}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success">المدفوعات</h6>
                    <p><strong>المبلغ المدفوع:</strong> ${window.samApp.formatCurrency(paidAmount)}</p>
                    <p><strong>المبلغ المتبقي:</strong> ${window.samApp.formatCurrency(remainingAmount)}</p>
                    <p><strong>نسبة السداد:</strong> ${((paidAmount / (advance.amount || 1)) * 100).toFixed(1)}%</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-info">معلومات إضافية</h6>
                    <p><strong>السبب:</strong> ${advance.reason || 'غير محدد'}</p>
                    <p><strong>ملاحظات:</strong> ${advance.notes || 'لا توجد'}</p>
                </div>
            </div>

            ${advancePayments.length > 0 ? `
            <div class="row mt-4">
                <div class="col-12">
                    <h6 class="text-primary">سجل المدفوعات</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${advancePayments.map(payment => `
                                    <tr>
                                        <td>${window.samApp.formatDate(payment.payment_date)}</td>
                                        <td>${window.samApp.formatCurrency(payment.payment_amount)}</td>
                                        <td>${this.getPaymentMethodText(payment.payment_method)}</td>
                                        <td>${payment.payment_notes || '-'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            ` : ''}
        `;

        this.selectedAdvanceForPayment = advance;
        modal.show();
    }

    getPaymentMethodText(method) {
        const methodMap = {
            'salary_deduction': 'خصم من الراتب',
            'cash': 'نقداً',
            'bank_transfer': 'تحويل بنكي'
        };
        return methodMap[method] || 'غير محدد';
    }

    showPaymentModal(advanceId) {
        const advance = Database.getById('advances', advanceId);
        if (!advance) {
            window.samApp.showAlert('السلفة غير موجودة', 'danger');
            return;
        }

        const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
        const form = document.getElementById('paymentForm');

        // Set default values
        form.reset();
        form.querySelector('input[name="payment_amount"]').value = advance.monthly_installment || 0;
        form.querySelector('input[name="payment_date"]').value = new Date().toISOString().split('T')[0];
        form.querySelector('select[name="payment_method"]').value = 'salary_deduction';

        this.selectedAdvanceForPayment = advance;
        modal.show();
    }

    savePayment() {
        const form = document.getElementById('paymentForm');
        const formData = new FormData(form);
        const paymentData = Object.fromEntries(formData.entries());

        if (!this.selectedAdvanceForPayment) {
            window.samApp.showAlert('لم يتم تحديد السلفة', 'danger');
            return;
        }

        paymentData.advance_id = this.selectedAdvanceForPayment.id;
        paymentData.payment_amount = parseFloat(paymentData.payment_amount) || 0;

        try {
            // Create payment record
            Database.create('advance_payments', paymentData);

            // Check if advance is fully paid
            const paidAmount = this.calculatePaidAmount(this.selectedAdvanceForPayment) + paymentData.payment_amount;
            if (paidAmount >= this.selectedAdvanceForPayment.amount) {
                Database.update('advances', this.selectedAdvanceForPayment.id, { status: 'completed' });
            }

            window.samApp.showAlert('تم تسجيل الدفع بنجاح', 'success');

            const modal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
            modal.hide();
            this.loadAdvancesData();

        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    deleteAdvance(advanceId) {
        if (confirm('هل أنت متأكد من حذف السلفة؟ سيتم حذف جميع المدفوعات المرتبطة بها.')) {
            try {
                // Delete advance payments first
                const payments = Database.getAll('advance_payments') || [];
                payments.filter(p => p.advance_id === advanceId).forEach(payment => {
                    Database.delete('advance_payments', payment.id);
                });

                // Delete advance
                Database.delete('advances', advanceId);
                window.samApp.showAlert('تم حذف السلفة بنجاح', 'success');
                this.loadAdvancesData();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    exportAdvances() {
        const advances = Database.getAll('advances') || [];
        const employees = Database.getEmployees();

        const exportData = advances.map(advance => {
            const employee = employees.find(emp => emp.id === advance.employee_id);
            const paidAmount = this.calculatePaidAmount(advance);
            const remainingAmount = (advance.amount || 0) - paidAmount;

            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'نوع السلفة': this.getAdvanceTypeText(advance.advance_type),
                'المبلغ': advance.amount,
                'عدد الأقساط': advance.installments,
                'القسط الشهري': advance.monthly_installment,
                'معدل الفائدة': advance.interest_rate,
                'المبلغ المدفوع': paidAmount,
                'المبلغ المتبقي': remainingAmount,
                'الحالة': advance.status,
                'تاريخ البداية': advance.start_date,
                'السبب': advance.reason || '',
                'ملاحظات': advance.notes || '',
                'تاريخ الإنشاء': advance.created_at
            };
        });

        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'السلف');
        XLSX.writeFile(wb, `advances_${new Date().toISOString().split('T')[0]}.xlsx`);

        window.samApp.showAlert('تم تصدير بيانات السلف بنجاح', 'success');
    }
}

// Global reference for modal actions
let advancesManager;
