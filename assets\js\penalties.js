/**
 * SAM - نظام إدارة شؤون الموظفين
 * Penalties Management Module
 * وحدة إدارة الجزاءات والعقوبات
 */

class PenaltiesManager {
    constructor() {
        this.currentView = 'all';
        this.selectedEmployee = '';
        this.selectedType = '';
    }

    render() {
        if (!window.authManager.hasPermission('employees')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadPenaltiesData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-gavel me-2"></i>
                            إدارة الجزاءات والعقوبات
                        </h2>
                        <button class="btn btn-primary" id="addPenaltyBtn">
                            <i class="fas fa-plus me-2"></i>
                            إضافة جزاء جديد
                        </button>
                    </div>
                </div>
            </div>

            <!-- Penalties Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalPenalties">0</h4>
                                    <p class="mb-0">إجمالي الجزاءات</p>
                                </div>
                                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="warningsCount">0</h4>
                                    <p class="mb-0">إنذارات</p>
                                </div>
                                <i class="fas fa-exclamation fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="deductionsCount">0</h4>
                                    <p class="mb-0">خصومات مالية</p>
                                </div>
                                <i class="fas fa-minus-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalAmount">0</h4>
                                    <p class="mb-0">إجمالي المبلغ</p>
                                </div>
                                <i class="fas fa-money-bill fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="typeFilter">
                        <option value="">جميع الأنواع</option>
                        <option value="warning">إنذار</option>
                        <option value="financial_deduction">خصم مالي</option>
                        <option value="suspension">إيقاف مؤقت</option>
                        <option value="termination">فصل</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="severityFilter">
                        <option value="">جميع الدرجات</option>
                        <option value="minor">بسيط</option>
                        <option value="moderate">متوسط</option>
                        <option value="major">شديد</option>
                        <option value="critical">حرج</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-success w-100" id="exportPenaltiesBtn">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <!-- Penalties Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        سجل الجزاءات والعقوبات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الجزاء</th>
                                    <th>الدرجة</th>
                                    <th>السبب</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="penaltiesTableBody">
                                <!-- Penalty records will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Penalty Modal -->
            <div class="modal fade" id="penaltyModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="penaltyModalTitle">إضافة جزاء جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="penaltyForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع الجزاء *</label>
                                        <select class="form-select" name="penalty_type" required>
                                            <option value="">اختر نوع الجزاء</option>
                                            <option value="warning">إنذار</option>
                                            <option value="financial_deduction">خصم مالي</option>
                                            <option value="suspension">إيقاف مؤقت</option>
                                            <option value="termination">فصل</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">درجة الجزاء *</label>
                                        <select class="form-select" name="severity" required>
                                            <option value="">اختر الدرجة</option>
                                            <option value="minor">بسيط</option>
                                            <option value="moderate">متوسط</option>
                                            <option value="major">شديد</option>
                                            <option value="critical">حرج</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الجزاء *</label>
                                        <input type="date" class="form-control" name="penalty_date" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المبلغ (إن وجد)</label>
                                        <input type="number" class="form-control" name="amount" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">مدة الإيقاف (أيام)</label>
                                        <input type="number" class="form-control" name="suspension_days" min="0">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="active">نشط</option>
                                            <option value="completed">مكتمل</option>
                                            <option value="cancelled">ملغي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">يؤثر على الراتب؟</label>
                                        <select class="form-select" name="affects_salary">
                                            <option value="no">لا</option>
                                            <option value="yes">نعم</option>
                                        </select>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">سبب الجزاء *</label>
                                        <textarea class="form-control" name="reason" rows="3" required></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">تفاصيل الجزاء</label>
                                        <textarea class="form-control" name="details" rows="3"></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات الإدارة</label>
                                        <textarea class="form-control" name="admin_notes" rows="2"></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">الإجراءات المطلوبة</label>
                                        <textarea class="form-control" name="required_actions" rows="2"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="savePenaltyBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Penalty Details Modal -->
            <div class="modal fade" id="penaltyDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل الجزاء</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="penaltyDetailsContent">
                            <!-- Penalty details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="editPenaltyBtn">تعديل</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Add penalty button
        document.getElementById('addPenaltyBtn').addEventListener('click', () => {
            this.showPenaltyModal();
        });

        // Filter events
        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadPenaltiesData();
        });

        document.getElementById('typeFilter').addEventListener('change', (e) => {
            this.selectedType = e.target.value;
            this.loadPenaltiesData();
        });

        // Export button
        document.getElementById('exportPenaltiesBtn').addEventListener('click', () => {
            this.exportPenalties();
        });

        // Save penalty
        document.getElementById('savePenaltyBtn').addEventListener('click', () => {
            this.savePenalty();
        });
    }

    loadPenaltiesData() {
        this.loadEmployeeOptions();
        this.loadPenaltiesTable();
        this.updatePenaltyStats();
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const selects = document.querySelectorAll('#employeeFilter, select[name="employee_id"]');

        selects.forEach(select => {
            const isFilter = select.id === 'employeeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الموظفين</option>' : '<option value="">اختر الموظف</option>';

            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadPenaltiesTable() {
        let penalties = Database.getAll('penalties') || [];

        // Apply filters
        if (this.selectedEmployee) {
            penalties = penalties.filter(pen => pen.employee_id === this.selectedEmployee);
        }

        if (this.selectedType) {
            penalties = penalties.filter(pen => pen.penalty_type === this.selectedType);
        }

        // Sort by creation date (newest first)
        penalties.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        this.renderPenaltiesTable(penalties);
    }

    renderPenaltiesTable(penalties) {
        const tbody = document.getElementById('penaltiesTableBody');
        const employees = Database.getEmployees();

        if (penalties.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="fas fa-gavel fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد جزاءات</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = penalties.map(penalty => {
            const employee = employees.find(emp => emp.id === penalty.employee_id);
            const statusBadge = this.getStatusBadge(penalty.status);
            const severityBadge = this.getSeverityBadge(penalty.severity);

            return `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${employee?.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}"
                                 alt="${employee?.name}" class="rounded-circle me-2" width="40" height="40">
                            <div>
                                <div class="fw-bold">${employee?.name || 'غير معروف'}</div>
                                <small class="text-muted">${employee?.employee_number || ''}</small>
                            </div>
                        </div>
                    </td>
                    <td>${this.getPenaltyTypeText(penalty.penalty_type)}</td>
                    <td>${severityBadge}</td>
                    <td>
                        <span class="text-truncate" style="max-width: 200px; display: inline-block;"
                              title="${penalty.reason || ''}">${penalty.reason || 'غير محدد'}</span>
                    </td>
                    <td>${penalty.amount ? window.samApp.formatCurrency(penalty.amount) : '-'}</td>
                    <td>${window.samApp.formatDate(penalty.penalty_date)}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary view-penalty"
                                    data-penalty-id="${penalty.id}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success edit-penalty"
                                    data-penalty-id="${penalty.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger delete-penalty"
                                    data-penalty-id="${penalty.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View penalty
        document.querySelectorAll('.view-penalty').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const penaltyId = e.target.closest('.view-penalty').dataset.penaltyId;
                this.viewPenalty(penaltyId);
            });
        });

        // Edit penalty
        document.querySelectorAll('.edit-penalty').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const penaltyId = e.target.closest('.edit-penalty').dataset.penaltyId;
                const penalty = Database.getById('penalties', penaltyId);
                this.showPenaltyModal(penalty);
            });
        });

        // Delete penalty
        document.querySelectorAll('.delete-penalty').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const penaltyId = e.target.closest('.delete-penalty').dataset.penaltyId;
                this.deletePenalty(penaltyId);
            });
        });
    }

    getStatusBadge(status) {
        const statusMap = {
            'active': { class: 'warning', text: 'نشط' },
            'completed': { class: 'success', text: 'مكتمل' },
            'cancelled': { class: 'secondary', text: 'ملغي' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    getSeverityBadge(severity) {
        const severityMap = {
            'minor': { class: 'info', text: 'بسيط' },
            'moderate': { class: 'warning', text: 'متوسط' },
            'major': { class: 'danger', text: 'شديد' },
            'critical': { class: 'dark', text: 'حرج' }
        };

        const severityInfo = severityMap[severity] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${severityInfo.class}">${severityInfo.text}</span>`;
    }

    getPenaltyTypeText(type) {
        const typeMap = {
            'warning': 'إنذار',
            'financial_deduction': 'خصم مالي',
            'suspension': 'إيقاف مؤقت',
            'termination': 'فصل',
            'other': 'أخرى'
        };
        return typeMap[type] || 'غير محدد';
    }

    updatePenaltyStats() {
        const penalties = Database.getAll('penalties') || [];

        const totalPenalties = penalties.length;
        const warningsCount = penalties.filter(p => p.penalty_type === 'warning').length;
        const deductionsCount = penalties.filter(p => p.penalty_type === 'financial_deduction').length;
        const totalAmount = penalties.reduce((sum, p) => sum + (parseFloat(p.amount) || 0), 0);

        document.getElementById('totalPenalties').textContent = totalPenalties;
        document.getElementById('warningsCount').textContent = warningsCount;
        document.getElementById('deductionsCount').textContent = deductionsCount;
        document.getElementById('totalAmount').textContent = window.samApp.formatCurrency(totalAmount);
    }

    showPenaltyModal(penalty = null) {
        const modal = new bootstrap.Modal(document.getElementById('penaltyModal'));
        const form = document.getElementById('penaltyForm');
        const title = document.getElementById('penaltyModalTitle');

        if (penalty) {
            title.textContent = 'تعديل الجزاء';
            this.populatePenaltyForm(form, penalty);
            this.selectedPenalty = penalty;
        } else {
            title.textContent = 'إضافة جزاء جديد';
            form.reset();
            form.querySelector('input[name="penalty_date"]').value = new Date().toISOString().split('T')[0];
            form.querySelector('select[name="status"]').value = 'active';
            this.selectedPenalty = null;
        }

        modal.show();
    }

    populatePenaltyForm(form, penalty) {
        Object.keys(penalty).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = penalty[key] || '';
            }
        });
    }

    savePenalty() {
        const form = document.getElementById('penaltyForm');
        const formData = new FormData(form);
        const penaltyData = Object.fromEntries(formData.entries());

        // Convert numeric fields
        const numericFields = ['amount', 'suspension_days'];
        numericFields.forEach(field => {
            if (penaltyData[field]) {
                penaltyData[field] = parseFloat(penaltyData[field]) || 0;
            }
        });

        try {
            if (this.selectedPenalty) {
                // Update existing penalty
                Database.update('penalties', this.selectedPenalty.id, penaltyData);
                window.samApp.showAlert('تم تحديث الجزاء بنجاح', 'success');
            } else {
                // Create new penalty
                Database.create('penalties', penaltyData);
                window.samApp.showAlert('تم إضافة الجزاء بنجاح', 'success');
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('penaltyModal'));
            modal.hide();
            this.loadPenaltiesData();

        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    viewPenalty(penaltyId) {
        const penalty = Database.getById('penalties', penaltyId);
        if (!penalty) {
            window.samApp.showAlert('الجزاء غير موجود', 'danger');
            return;
        }

        this.showPenaltyDetails(penalty);
    }

    showPenaltyDetails(penalty) {
        const modal = new bootstrap.Modal(document.getElementById('penaltyDetailsModal'));
        const content = document.getElementById('penaltyDetailsContent');

        const employee = Database.getEmployee(penalty.employee_id);

        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات الموظف</h6>
                    <p><strong>الاسم:</strong> ${employee?.name || 'غير معروف'}</p>
                    <p><strong>الرقم الوظيفي:</strong> ${employee?.employee_number || ''}</p>
                    <p><strong>القسم:</strong> ${employee?.department || 'غير محدد'}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">تفاصيل الجزاء</h6>
                    <p><strong>نوع الجزاء:</strong> ${this.getPenaltyTypeText(penalty.penalty_type)}</p>
                    <p><strong>درجة الجزاء:</strong> ${this.getSeverityBadge(penalty.severity)}</p>
                    <p><strong>التاريخ:</strong> ${window.samApp.formatDate(penalty.penalty_date)}</p>
                    <p><strong>المبلغ:</strong> ${penalty.amount ? window.samApp.formatCurrency(penalty.amount) : 'لا يوجد'}</p>
                    <p><strong>مدة الإيقاف:</strong> ${penalty.suspension_days ? penalty.suspension_days + ' يوم' : 'لا يوجد'}</p>
                    <p><strong>الحالة:</strong> ${this.getStatusBadge(penalty.status)}</p>
                    <p><strong>يؤثر على الراتب:</strong> ${penalty.affects_salary === 'yes' ? 'نعم' : 'لا'}</p>
                </div>
                <div class="col-12">
                    <h6 class="text-primary">سبب الجزاء</h6>
                    <p>${penalty.reason || 'غير محدد'}</p>
                </div>
                ${penalty.details ? `
                <div class="col-12">
                    <h6 class="text-primary">تفاصيل الجزاء</h6>
                    <p>${penalty.details}</p>
                </div>
                ` : ''}
                ${penalty.admin_notes ? `
                <div class="col-12">
                    <h6 class="text-primary">ملاحظات الإدارة</h6>
                    <p>${penalty.admin_notes}</p>
                </div>
                ` : ''}
                ${penalty.required_actions ? `
                <div class="col-12">
                    <h6 class="text-primary">الإجراءات المطلوبة</h6>
                    <p>${penalty.required_actions}</p>
                </div>
                ` : ''}
            </div>
        `;

        this.selectedPenalty = penalty;
        modal.show();
    }

    deletePenalty(penaltyId) {
        if (confirm('هل أنت متأكد من حذف الجزاء؟')) {
            try {
                Database.delete('penalties', penaltyId);
                window.samApp.showAlert('تم حذف الجزاء بنجاح', 'success');
                this.loadPenaltiesData();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    exportPenalties() {
        const penalties = Database.getAll('penalties') || [];
        const employees = Database.getEmployees();

        const exportData = penalties.map(penalty => {
            const employee = employees.find(emp => emp.id === penalty.employee_id);

            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'نوع الجزاء': this.getPenaltyTypeText(penalty.penalty_type),
                'درجة الجزاء': penalty.severity,
                'التاريخ': penalty.penalty_date,
                'المبلغ': penalty.amount || 0,
                'مدة الإيقاف': penalty.suspension_days || 0,
                'الحالة': penalty.status,
                'يؤثر على الراتب': penalty.affects_salary === 'yes' ? 'نعم' : 'لا',
                'السبب': penalty.reason || '',
                'التفاصيل': penalty.details || '',
                'ملاحظات الإدارة': penalty.admin_notes || '',
                'الإجراءات المطلوبة': penalty.required_actions || '',
                'تاريخ الإنشاء': penalty.created_at
            };
        });

        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الجزاءات والعقوبات');
        XLSX.writeFile(wb, `penalties_${new Date().toISOString().split('T')[0]}.xlsx`);

        window.samApp.showAlert('تم تصدير بيانات الجزاءات بنجاح', 'success');
    }
}

// Global reference for modal actions
let penaltiesManager;
