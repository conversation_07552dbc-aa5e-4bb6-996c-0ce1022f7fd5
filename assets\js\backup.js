/**
 * SAM - نظام إدارة شؤون الموظفين
 * Backup Management Module
 * وحدة إدارة النسخ الاحتياطية
 */

class BackupManager {
    constructor() {
        this.backupHistory = [];
    }

    render() {
        if (!window.authManager.hasPermission('backup')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadBackupHistory();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-database me-2"></i>
                        إدارة النسخ الاحتياطية
                    </h2>
                </div>
            </div>

            <!-- Backup Actions -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-download me-2"></i>
                                إنشاء نسخة احتياطية
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">قم بإنشاء نسخة احتياطية كاملة من جميع بيانات النظام</p>
                            <div class="mb-3">
                                <label class="form-label">اختر البيانات المراد نسخها:</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backupEmployees" checked>
                                    <label class="form-check-label" for="backupEmployees">بيانات الموظفين</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backupAttendance" checked>
                                    <label class="form-check-label" for="backupAttendance">سجلات الحضور</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backupLeaves" checked>
                                    <label class="form-check-label" for="backupLeaves">الإجازات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backupPayroll" checked>
                                    <label class="form-check-label" for="backupPayroll">الرواتب</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backupAdvances" checked>
                                    <label class="form-check-label" for="backupAdvances">السلف</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backupPenalties" checked>
                                    <label class="form-check-label" for="backupPenalties">الجزاءات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backupPermissions" checked>
                                    <label class="form-check-label" for="backupPermissions">الأذونات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backupUsers" checked>
                                    <label class="form-check-label" for="backupUsers">المستخدمين</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backupSettings" checked>
                                    <label class="form-check-label" for="backupSettings">الإعدادات</label>
                                </div>
                            </div>
                            <button class="btn btn-primary" id="createBackupBtn">
                                <i class="fas fa-download me-2"></i>
                                إنشاء وتحميل النسخة الاحتياطية
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-upload me-2"></i>
                                استعادة نسخة احتياطية
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">قم باستعادة البيانات من نسخة احتياطية سابقة</p>
                            <div class="mb-3">
                                <label class="form-label">اختر ملف النسخة الاحتياطية:</label>
                                <input type="file" class="form-control" id="restoreFile" accept=".json">
                                <div class="form-text">يجب أن يكون الملف بصيغة JSON</div>
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> ستؤدي عملية الاستعادة إلى استبدال جميع البيانات الحالية
                            </div>
                            <button class="btn btn-success" id="restoreBackupBtn">
                                <i class="fas fa-upload me-2"></i>
                                استعادة النسخة الاحتياطية
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Statistics -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                إحصائيات قاعدة البيانات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row" id="databaseStats">
                                <!-- Statistics will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup History -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>
                                سجل النسخ الاحتياطية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>التاريخ والوقت</th>
                                            <th>نوع العملية</th>
                                            <th>حجم الملف</th>
                                            <th>عدد السجلات</th>
                                            <th>المستخدم</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody id="backupHistoryBody">
                                        <!-- Backup history will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Management Tools -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                أدوات إدارة البيانات المتقدمة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>مسح البيانات</h6>
                                    <p class="text-muted">حذف جميع البيانات من النظام</p>
                                    <button class="btn btn-outline-danger" id="clearAllDataBtn">
                                        <i class="fas fa-trash me-2"></i>
                                        مسح جميع البيانات
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <h6>إعادة تعيين النظام</h6>
                                    <p class="text-muted">إعادة النظام إلى الإعدادات الافتراضية</p>
                                    <button class="btn btn-outline-warning" id="resetSystemBtn">
                                        <i class="fas fa-redo me-2"></i>
                                        إعادة تعيين النظام
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <h6>تحسين قاعدة البيانات</h6>
                                    <p class="text-muted">تنظيف وتحسين أداء قاعدة البيانات</p>
                                    <button class="btn btn-outline-info" id="optimizeDbBtn">
                                        <i class="fas fa-tools me-2"></i>
                                        تحسين قاعدة البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Create backup
        document.getElementById('createBackupBtn').addEventListener('click', () => {
            this.createBackup();
        });

        // Restore backup
        document.getElementById('restoreBackupBtn').addEventListener('click', () => {
            this.restoreBackup();
        });

        // Clear all data
        document.getElementById('clearAllDataBtn').addEventListener('click', () => {
            this.clearAllData();
        });

        // Reset system
        document.getElementById('resetSystemBtn').addEventListener('click', () => {
            this.resetSystem();
        });

        // Optimize database
        document.getElementById('optimizeDbBtn').addEventListener('click', () => {
            this.optimizeDatabase();
        });
    }

    loadBackupHistory() {
        this.loadDatabaseStats();
        this.loadBackupHistoryTable();
    }

    loadDatabaseStats() {
        const stats = Database.getStatistics();
        const tables = [
            { name: 'employees', label: 'الموظفين', icon: 'users' },
            { name: 'attendance', label: 'الحضور', icon: 'clock' },
            { name: 'leaves', label: 'الإجازات', icon: 'calendar-alt' },
            { name: 'payroll', label: 'الرواتب', icon: 'money-bill-wave' },
            { name: 'advances', label: 'السلف', icon: 'hand-holding-usd' },
            { name: 'penalties', label: 'الجزاءات', icon: 'gavel' },
            { name: 'permissions', label: 'الأذونات', icon: 'user-clock' },
            { name: 'users', label: 'المستخدمين', icon: 'users-cog' }
        ];

        const container = document.getElementById('databaseStats');
        container.innerHTML = tables.map(table => {
            const count = Database.getAll(table.name).length;
            return `
                <div class="col-md-3 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-${table.icon} fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="mb-0">${count}</h5>
                            <small class="text-muted">${table.label}</small>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    loadBackupHistoryTable() {
        const history = JSON.parse(localStorage.getItem('sam_backup_history') || '[]');
        const tbody = document.getElementById('backupHistoryBody');
        
        if (history.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4">
                        <i class="fas fa-history fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا يوجد سجل للنسخ الاحتياطية</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = history.reverse().map(entry => `
            <tr>
                <td>${window.samApp.formatDateTime(entry.timestamp)}</td>
                <td>
                    <span class="badge bg-${entry.type === 'backup' ? 'primary' : 'success'}">
                        ${entry.type === 'backup' ? 'نسخ احتياطي' : 'استعادة'}
                    </span>
                </td>
                <td>${entry.size || '-'}</td>
                <td>${entry.records || '-'}</td>
                <td>${entry.user || 'غير معروف'}</td>
                <td>
                    <span class="badge bg-${entry.status === 'success' ? 'success' : 'danger'}">
                        ${entry.status === 'success' ? 'نجح' : 'فشل'}
                    </span>
                </td>
            </tr>
        `).join('');
    }

    createBackup() {
        try {
            const selectedTables = this.getSelectedTables();
            if (selectedTables.length === 0) {
                window.samApp.showAlert('يرجى اختيار البيانات المراد نسخها', 'warning');
                return;
            }

            const backupData = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                tables: selectedTables,
                data: {}
            };

            let totalRecords = 0;
            selectedTables.forEach(table => {
                const data = Database.getAll(table);
                backupData.data[table] = data;
                totalRecords += data.length;
            });

            // Add system info
            backupData.system_info = {
                user_agent: navigator.userAgent,
                created_by: window.authManager.getCurrentUser()?.username || 'unknown'
            };

            const backupJson = JSON.stringify(backupData, null, 2);
            const blob = new Blob([backupJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            // Create download link
            const a = document.createElement('a');
            a.href = url;
            a.download = `sam_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            // Log backup operation
            this.logBackupOperation('backup', 'success', {
                size: this.formatFileSize(blob.size),
                records: totalRecords,
                tables: selectedTables
            });

            window.samApp.showAlert('تم إنشاء النسخة الاحتياطية وتحميلها بنجاح', 'success');
            this.loadBackupHistoryTable();

        } catch (error) {
            this.logBackupOperation('backup', 'failed', { error: error.message });
            window.samApp.showAlert('حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' + error.message, 'danger');
        }
    }

    getSelectedTables() {
        const checkboxes = [
            'backupEmployees', 'backupAttendance', 'backupLeaves', 'backupPayroll',
            'backupAdvances', 'backupPenalties', 'backupPermissions', 'backupUsers', 'backupSettings'
        ];

        const tableMap = {
            'backupEmployees': 'employees',
            'backupAttendance': 'attendance',
            'backupLeaves': 'leaves',
            'backupPayroll': 'payroll',
            'backupAdvances': 'advances',
            'backupPenalties': 'penalties',
            'backupPermissions': 'permissions',
            'backupUsers': 'users',
            'backupSettings': 'settings'
        };

        return checkboxes
            .filter(id => document.getElementById(id).checked)
            .map(id => tableMap[id]);
    }

    restoreBackup() {
        const fileInput = document.getElementById('restoreFile');
        const file = fileInput.files[0];

        if (!file) {
            window.samApp.showAlert('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
            return;
        }

        if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال جميع البيانات الحالية!')) {
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const backupData = JSON.parse(e.target.result);
                this.processRestore(backupData);
            } catch (error) {
                this.logBackupOperation('restore', 'failed', { error: error.message });
                window.samApp.showAlert('ملف النسخة الاحتياطية غير صالح: ' + error.message, 'danger');
            }
        };

        reader.readAsText(file);
    }

    processRestore(backupData) {
        try {
            if (!backupData.version || !backupData.data) {
                throw new Error('تنسيق ملف النسخة الاحتياطية غير صحيح');
            }

            let restoredRecords = 0;
            const restoredTables = [];

            // Clear existing data and restore from backup
            Object.keys(backupData.data).forEach(table => {
                if (table === 'users') {
                    // Special handling for users to preserve current user
                    this.restoreUsersTable(backupData.data[table]);
                } else {
                    Database.clearTable(table);
                    backupData.data[table].forEach(record => {
                        Database.create(table, record);
                        restoredRecords++;
                    });
                }
                restoredTables.push(table);
            });

            // Log restore operation
            this.logBackupOperation('restore', 'success', {
                records: restoredRecords,
                tables: restoredTables,
                backup_date: backupData.timestamp
            });

            window.samApp.showAlert(`تم استعادة النسخة الاحتياطية بنجاح\nتم استعادة ${restoredRecords} سجل`, 'success');

            // Refresh the page to reload all data
            setTimeout(() => {
                location.reload();
            }, 2000);

        } catch (error) {
            this.logBackupOperation('restore', 'failed', { error: error.message });
            window.samApp.showAlert('حدث خطأ أثناء استعادة النسخة الاحتياطية: ' + error.message, 'danger');
        }
    }

    restoreUsersTable(users) {
        const currentUser = window.authManager.getCurrentUser();

        // Clear users table but preserve current user
        Database.clearTable('users');

        // Restore users from backup
        users.forEach(user => {
            Database.create('users', user);
        });

        // Ensure current user still exists
        if (currentUser && !users.find(u => u.id === currentUser.id)) {
            Database.create('users', currentUser);
        }
    }

    clearAllData() {
        if (!confirm('هل أنت متأكد من حذف جميع البيانات؟\nهذا الإجراء لا يمكن التراجع عنه!')) {
            return;
        }

        if (!confirm('تأكيد أخير: سيتم حذف جميع البيانات نهائياً!')) {
            return;
        }

        try {
            const tables = ['employees', 'attendance', 'leaves', 'payroll', 'advances', 'penalties', 'permissions'];
            let clearedRecords = 0;

            tables.forEach(table => {
                const count = Database.getAll(table).length;
                Database.clearTable(table);
                clearedRecords += count;
            });

            this.logBackupOperation('clear_data', 'success', { records: clearedRecords });
            window.samApp.showAlert(`تم حذف ${clearedRecords} سجل بنجاح`, 'success');
            this.loadDatabaseStats();

        } catch (error) {
            this.logBackupOperation('clear_data', 'failed', { error: error.message });
            window.samApp.showAlert('حدث خطأ أثناء حذف البيانات: ' + error.message, 'danger');
        }
    }

    resetSystem() {
        if (!confirm('هل أنت متأكد من إعادة تعيين النظام؟\nسيتم حذف جميع البيانات وإعادة الإعدادات الافتراضية!')) {
            return;
        }

        if (!confirm('تأكيد أخير: سيتم إعادة تعيين النظام بالكامل!')) {
            return;
        }

        try {
            // Clear all data
            const tables = ['employees', 'attendance', 'leaves', 'payroll', 'advances', 'penalties', 'permissions'];
            tables.forEach(table => {
                Database.clearTable(table);
            });

            // Reset settings to default
            Database.resetSettings();

            // Keep only admin user
            const adminUser = Database.getAll('users').find(u => u.id === 'admin');
            Database.clearTable('users');
            if (adminUser) {
                Database.create('users', adminUser);
            }

            this.logBackupOperation('reset_system', 'success', {});
            window.samApp.showAlert('تم إعادة تعيين النظام بنجاح', 'success');

            // Refresh the page
            setTimeout(() => {
                location.reload();
            }, 2000);

        } catch (error) {
            this.logBackupOperation('reset_system', 'failed', { error: error.message });
            window.samApp.showAlert('حدث خطأ أثناء إعادة تعيين النظام: ' + error.message, 'danger');
        }
    }

    optimizeDatabase() {
        try {
            let optimizedTables = 0;
            let removedRecords = 0;

            // Remove duplicate records
            const tables = ['employees', 'attendance', 'leaves', 'payroll', 'advances', 'penalties', 'permissions'];

            tables.forEach(table => {
                const records = Database.getAll(table);
                const uniqueRecords = this.removeDuplicates(records);

                if (records.length !== uniqueRecords.length) {
                    Database.clearTable(table);
                    uniqueRecords.forEach(record => {
                        Database.create(table, record);
                    });
                    removedRecords += records.length - uniqueRecords.length;
                    optimizedTables++;
                }
            });

            // Clean up orphaned records
            this.cleanupOrphanedRecords();

            this.logBackupOperation('optimize', 'success', {
                tables: optimizedTables,
                removed_records: removedRecords
            });

            window.samApp.showAlert(`تم تحسين قاعدة البيانات بنجاح\nتم تحسين ${optimizedTables} جدول وحذف ${removedRecords} سجل مكرر`, 'success');
            this.loadDatabaseStats();

        } catch (error) {
            this.logBackupOperation('optimize', 'failed', { error: error.message });
            window.samApp.showAlert('حدث خطأ أثناء تحسين قاعدة البيانات: ' + error.message, 'danger');
        }
    }

    removeDuplicates(records) {
        const seen = new Set();
        return records.filter(record => {
            const key = JSON.stringify(record);
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    cleanupOrphanedRecords() {
        const employees = Database.getAll('employees');
        const employeeIds = employees.map(emp => emp.id);

        // Clean attendance records
        const attendance = Database.getAll('attendance');
        const validAttendance = attendance.filter(att => employeeIds.includes(att.employee_id));
        if (attendance.length !== validAttendance.length) {
            Database.clearTable('attendance');
            validAttendance.forEach(record => Database.create('attendance', record));
        }

        // Clean other employee-related records
        const tables = ['leaves', 'payroll', 'advances', 'penalties', 'permissions'];
        tables.forEach(table => {
            const records = Database.getAll(table);
            const validRecords = records.filter(record => employeeIds.includes(record.employee_id));
            if (records.length !== validRecords.length) {
                Database.clearTable(table);
                validRecords.forEach(record => Database.create(table, record));
            }
        });
    }

    logBackupOperation(type, status, details = {}) {
        const history = JSON.parse(localStorage.getItem('sam_backup_history') || '[]');

        const entry = {
            timestamp: new Date().toISOString(),
            type: type,
            status: status,
            user: window.authManager.getCurrentUser()?.username || 'unknown',
            ...details
        };

        history.push(entry);

        // Keep only last 50 entries
        if (history.length > 50) {
            history.splice(0, history.length - 50);
        }

        localStorage.setItem('sam_backup_history', JSON.stringify(history));
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Global reference
let backupManager;
