/**
 * SAM - نظام إدارة شؤون الموظفين
 * Payroll Management Module
 * وحدة إدارة الرواتب
 */

class PayrollManager {
    constructor() {
        this.currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM
        this.selectedEmployee = '';
        this.selectedStatus = '';
        this.selectedPayroll = null;
        this.payrollDetails = null;
        this.lastCalculation = null;
        this.autoRefreshInterval = null;
        this.searchQuery = '';
    }

    render() {
        if (!window.authManager.hasPermission('payroll')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();

        // إضافة تأثير الانتقال
        contentArea.classList.add('fade-in');

        this.bindEvents();
        this.loadPayrollData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-money-bill-wave me-2"></i>
                            إدارة الرواتب
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-primary" id="generatePayrollBtn">
                                <i class="fas fa-calculator me-2"></i>
                                حساب الرواتب
                            </button>
                            <button class="btn btn-success" id="addPayrollBtn">
                                <i class="fas fa-plus me-2"></i>
                                إضافة راتب
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payroll Summary -->
            <div class="row mb-4 payroll-stats">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalPayroll">0</h4>
                                    <p class="mb-0">إجمالي الرواتب</p>
                                </div>
                                <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="paidCount">0</h4>
                                    <p class="mb-0">مدفوعة</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="pendingCount">0</h4>
                                    <p class="mb-0">في الانتظار</p>
                                </div>
                                <i class="fas fa-hourglass-half fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="employeeCount">0</h4>
                                    <p class="mb-0">عدد الموظفين</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <label class="form-label">الشهر</label>
                    <input type="month" class="form-control" id="monthFilter" value="${this.currentMonth}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو رقم الموظف...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الموظف</label>
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="pending">في الانتظار</option>
                        <option value="paid">مدفوعة</option>
                        <option value="cancelled">ملغية</option>
                        <option value="no_payroll">بدون كشف راتب</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary flex-fill" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-outline-warning" id="recalculateBtn" title="إعادة حساب جميع الرواتب">
                            <i class="fas fa-calculator"></i> إعادة حساب
                        </button>
                        <button class="btn btn-outline-success" id="exportPayrollBtn">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <button class="btn btn-outline-info" id="printPayrollBtn">
                            <i class="fas fa-print"></i> طباعة إجمالية
                        </button>
                    </div>
                </div>
            </div>

            <!-- Payroll Table -->
            <div class="card payroll-table">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        كشوف الرواتب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>الشهر</th>
                                    <th>الراتب الأساسي</th>
                                    <th>البدلات</th>
                                    <th>الحوافز</th>
                                    <th>الخصومات</th>
                                    <th>صافي الراتب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="payrollTableBody">
                                <!-- Payroll records will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payroll Modal -->
            <div class="modal fade payroll-modal" id="payrollModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="payrollModalTitle">إضافة راتب</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="payrollForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الشهر *</label>
                                        <input type="month" class="form-control" name="month" required>
                                    </div>
                                </div>

                                <!-- Basic Salary Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-money-bill me-2"></i>
                                            الراتب الأساسي
                                        </h6>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الراتب الأساسي</label>
                                        <input type="number" class="form-control" name="basic_salary" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">أيام العمل</label>
                                        <input type="number" class="form-control" name="working_days" min="0" max="31">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">أيام الحضور</label>
                                        <input type="number" class="form-control" name="attendance_days" min="0" max="31">
                                    </div>
                                </div>

                                <!-- Allowances Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-success mb-3">
                                            <i class="fas fa-plus-circle me-2"></i>
                                            البدلات والإضافات
                                        </h6>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">بدل سكن</label>
                                        <input type="number" class="form-control" name="housing_allowance" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">بدل مواصلات</label>
                                        <input type="number" class="form-control" name="transport_allowance" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">بدل طعام</label>
                                        <input type="number" class="form-control" name="food_allowance" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">بدلات أخرى</label>
                                        <input type="number" class="form-control" name="other_allowances" step="0.01" min="0">
                                    </div>
                                </div>

                                <!-- Bonuses Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-info mb-3">
                                            <i class="fas fa-gift me-2"></i>
                                            الحوافز والمكافآت
                                        </h6>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">حافز الأداء</label>
                                        <input type="number" class="form-control" name="performance_bonus" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">ساعات إضافية</label>
                                        <input type="number" class="form-control" name="overtime_hours" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">حوافز أخرى</label>
                                        <input type="number" class="form-control" name="other_bonuses" step="0.01" min="0">
                                    </div>
                                </div>

                                <!-- Deductions Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-danger mb-3">
                                            <i class="fas fa-minus-circle me-2"></i>
                                            الخصومات والاستقطاعات
                                        </h6>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">التأمينات الاجتماعية</label>
                                        <input type="number" class="form-control" name="social_insurance" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">ضريبة الدخل</label>
                                        <input type="number" class="form-control" name="income_tax" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">السلف</label>
                                        <input type="number" class="form-control" name="advances" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">خصم التأخير</label>
                                        <input type="number" class="form-control" name="lateness_deduction" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">خصم الغياب</label>
                                        <input type="number" class="form-control" name="absence_deduction" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">خصومات أخرى</label>
                                        <input type="number" class="form-control" name="other_deductions" step="0.01" min="0">
                                    </div>
                                </div>

                                <!-- Summary Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-calculator me-2"></i>
                                            ملخص الراتب
                                        </h6>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">إجمالي الاستحقاقات</label>
                                        <input type="number" class="form-control text-success fw-bold" name="gross_salary" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">إجمالي الخصومات</label>
                                        <input type="number" class="form-control text-danger fw-bold" name="total_deductions" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">صافي الراتب</label>
                                        <input type="number" class="form-control text-primary fw-bold" name="net_salary" step="0.01" readonly>
                                    </div>
                                </div>

                                <!-- Status and Notes -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="pending">في الانتظار</option>
                                            <option value="paid">مدفوعة</option>
                                            <option value="cancelled">ملغية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الدفع</label>
                                        <input type="date" class="form-control" name="payment_date">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="calculateBtn">حساب</button>
                            <button type="button" class="btn btn-success" id="savePayrollBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Generate payroll button
        document.getElementById('generatePayrollBtn').addEventListener('click', () => {
            this.generateMonthlyPayroll();
        });

        // Add payroll button
        document.getElementById('addPayrollBtn').addEventListener('click', () => {
            this.showPayrollModal();
        });

        // Filter events with auto-refresh
        document.getElementById('monthFilter').addEventListener('change', (e) => {
            this.currentMonth = e.target.value;
            this.loadPayrollData();
        });

        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadPayrollData();
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.selectedStatus = e.target.value;
            this.loadPayrollData();
        });

        // Search input
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.loadPayrollTable(); // تحديث الجدول فقط بدون إعادة تحميل كامل
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            const btn = document.getElementById('refreshBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            btn.disabled = true;

            setTimeout(() => {
                this.loadPayrollData();
                btn.innerHTML = originalText;
                btn.disabled = false;
                window.samApp.showAlert('تم تحديث البيانات بنجاح', 'success');
            }, 500);
        });

        // Recalculate button
        document.getElementById('recalculateBtn').addEventListener('click', () => {
            if (confirm('هل أنت متأكد من إعادة حساب جميع كشوف الرواتب للشهر المحدد؟')) {
                this.recalculateMonthlyPayrolls();
            }
        });

        // Export button
        document.getElementById('exportPayrollBtn').addEventListener('click', () => {
            this.exportPayroll();
        });

        // Print button
        document.getElementById('printPayrollBtn').addEventListener('click', () => {
            this.printPayrollSummary();
        });

        // Save payroll
        document.getElementById('savePayrollBtn').addEventListener('click', () => {
            this.savePayroll();
        });

        // Calculate button
        document.getElementById('calculateBtn').addEventListener('click', () => {
            this.calculatePayroll();
        });

        // Auto-calculate when values change
        this.bindCalculationEvents();

        // Auto-refresh every 30 seconds to show any updates
        this.autoRefreshInterval = setInterval(() => {
            // تحديث صامت للبيانات دون إظهار مؤشر التحميل
            this.updatePayrollStats();

            // تحديث الجدول فقط إذا لم يكن هناك نموذج مفتوح
            const modal = document.getElementById('payrollModal');
            if (!modal || !modal.classList.contains('show')) {
                this.loadPayrollTable();
            }
        }, 30000);
    }

    bindCalculationEvents() {
        const form = document.getElementById('payrollForm');
        const inputs = form.querySelectorAll('input[type="number"]');

        inputs.forEach(input => {
            if (!input.readOnly) {
                input.addEventListener('input', () => {
                    this.calculatePayroll();
                });
            }
        });

        // Employee selection change
        form.querySelector('select[name="employee_id"]').addEventListener('change', (e) => {
            this.loadEmployeeData(e.target.value);
        });
    }

    loadPayrollData() {
        // إظهار مؤشر التحميل
        const tableBody = document.getElementById('payrollTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2 text-muted">جاري تحميل البيانات...</p>
                    </td>
                </tr>
            `;
        }

        // تحميل البيانات
        setTimeout(() => {
            this.loadEmployeeOptions();
            this.loadPayrollTable();
            this.updatePayrollStats();
        }, 100);
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const selects = document.querySelectorAll('#employeeFilter, select[name="employee_id"]');

        selects.forEach(select => {
            const isFilter = select.id === 'employeeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الموظفين</option>' : '<option value="">اختر الموظف</option>';

            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadPayrollTable() {
        const employees = Database.getEmployees() || [];
        const activeEmployees = employees.filter(emp => emp.status === 'active');
        let payrolls = Database.getAll('payroll') || [];

        // إنشاء قائمة شاملة تتضمن جميع الموظفين النشطين
        const allEmployeePayrolls = [];

        activeEmployees.forEach(emp => {
            // البحث عن كشف راتب موجود للموظف في الشهر المحدد
            const existingPayroll = payrolls.find(p =>
                p.employee_id === emp.id && p.month === this.currentMonth
            );

            if (existingPayroll) {
                // إضافة كشف الراتب الموجود
                allEmployeePayrolls.push(existingPayroll);
            } else {
                // إنشاء سجل مؤقت للموظف بدون كشف راتب
                const tempPayroll = this.createTempPayrollRecord(emp, this.currentMonth);
                allEmployeePayrolls.push(tempPayroll);
            }
        });

        // تطبيق الفلاتر
        let filteredPayrolls = allEmployeePayrolls;

        // فلتر الموظف المحدد
        if (this.selectedEmployee && this.selectedEmployee !== '') {
            filteredPayrolls = filteredPayrolls.filter(p => p.employee_id === this.selectedEmployee);
        }

        // فلتر الحالة
        if (this.selectedStatus && this.selectedStatus !== '') {
            if (this.selectedStatus === 'no_payroll') {
                filteredPayrolls = filteredPayrolls.filter(p => p.isTemporary === true);
            } else {
                filteredPayrolls = filteredPayrolls.filter(p => p.status === this.selectedStatus && !p.isTemporary);
            }
        }

        // فلتر البحث النصي (إذا كان موجود)
        if (this.searchQuery && this.searchQuery.trim() !== '') {
            const query = this.searchQuery.toLowerCase().trim();
            filteredPayrolls = filteredPayrolls.filter(p => {
                const employee = employees.find(emp => emp.id === p.employee_id);
                return employee && (
                    employee.name.toLowerCase().includes(query) ||
                    employee.employee_number?.toLowerCase().includes(query) ||
                    employee.department?.toLowerCase().includes(query)
                );
            });
        }

        // ترتيب النتائج
        filteredPayrolls.sort((a, b) => {
            // الموظفين بدون كشوف رواتب أولاً
            if (a.isTemporary && !b.isTemporary) return -1;
            if (!a.isTemporary && b.isTemporary) return 1;

            // ثم حسب تاريخ الإنشاء
            const dateA = new Date(a.created_at || a.month + '-01');
            const dateB = new Date(b.created_at || b.month + '-01');
            return dateB - dateA;
        });

        this.renderPayrollTable(filteredPayrolls);
    }

    renderPayrollTable(payrolls) {
        const tbody = document.getElementById('payrollTableBody');
        const employees = Database.getEmployees();

        if (payrolls.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-5">
                        <div class="d-flex flex-column align-items-center">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted mb-2">لا توجد كشوف رواتب</h5>
                            <p class="text-muted mb-3">لم يتم العثور على كشوف رواتب للشهر المحدد</p>
                            <button class="btn btn-primary" onclick="document.getElementById('generatePayrollBtn').click()">
                                <i class="fas fa-plus me-2"></i>إنشاء كشوف رواتب
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = payrolls.map(payroll => {
            const employee = employees.find(emp => emp.id === payroll.employee_id);
            const statusBadge = this.getStatusBadge(payroll.status);

            // حساب القيم بدقة باستخدام دالة إعادة الحساب
            let calculation;
            if (payroll.isTemporary) {
                // للسجلات المؤقتة، استخدم القيم الموجودة
                calculation = {
                    totalAllowances: this.calculateAllowances(payroll),
                    totalBonuses: this.calculateBonuses(payroll),
                    grossSalary: payroll.gross_salary || 0,
                    netSalary: payroll.net_salary || 0,
                    totalDeductions: payroll.total_deductions || 0
                };
            } else {
                // للسجلات الحقيقية، أعد الحساب للتأكد من الدقة
                calculation = this.recalculatePayrollValues(payroll);
            }

            return `
                <tr ${payroll.isTemporary ? 'class="table-warning"' : ''}>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${employee?.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}"
                                 alt="${employee?.name}" class="rounded-circle me-2" width="40" height="40">
                            <div>
                                <div class="fw-bold">${employee?.name || 'غير معروف'}</div>
                                <small class="text-muted">${employee?.employee_number || ''}</small>
                                ${payroll.isTemporary ? '<small class="text-warning"><i class="fas fa-exclamation-triangle"></i> لم يتم إنشاء كشف راتب</small>' : ''}
                            </div>
                        </div>
                    </td>
                    <td>${this.formatMonth(payroll.month)}</td>
                    <td class="text-end">${window.samApp.formatCurrency(payroll.basic_salary || 0)}</td>
                    <td class="text-end text-success">${window.samApp.formatCurrency(calculation.totalAllowances)}</td>
                    <td class="text-end text-info">${window.samApp.formatCurrency(calculation.totalBonuses)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(calculation.totalDeductions)}</td>
                    <td class="text-end"><strong class="text-primary">${window.samApp.formatCurrency(calculation.netSalary)}</strong></td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            ${payroll.isTemporary ? `
                                <button class="btn btn-outline-primary create-payroll"
                                        data-employee-id="${payroll.employee_id}"
                                        data-month="${payroll.month}" title="إنشاء كشف راتب">
                                    <i class="fas fa-plus"></i>
                                </button>
                            ` : `
                                <button class="btn btn-outline-primary view-payroll"
                                        data-payroll-id="${payroll.id}" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-success edit-payroll"
                                        data-payroll-id="${payroll.id}" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info print-payroll"
                                        data-payroll-id="${payroll.id}" title="طباعة قسيمة الراتب">
                                    <i class="fas fa-print"></i>
                                </button>
                                ${payroll.status === 'pending' ? `
                                <button class="btn btn-outline-warning mark-paid"
                                        data-payroll-id="${payroll.id}" title="تحديد كمدفوع">
                                    <i class="fas fa-check"></i>
                                </button>
                                ` : ''}
                                <button class="btn btn-outline-danger delete-payroll"
                                        data-payroll-id="${payroll.id}" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            `}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View payroll
        document.querySelectorAll('.view-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.view-payroll').dataset.payrollId;
                this.viewPayroll(payrollId);
            });
        });

        // Edit payroll
        document.querySelectorAll('.edit-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.edit-payroll').dataset.payrollId;
                const payroll = Database.getById('payroll', payrollId);
                this.showPayrollModal(payroll);
            });
        });

        // Print payroll
        document.querySelectorAll('.print-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.print-payroll').dataset.payrollId;
                this.printPayslip(payrollId);
            });
        });

        // Mark as paid
        document.querySelectorAll('.mark-paid').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.mark-paid').dataset.payrollId;
                this.markAsPaid(payrollId);
            });
        });

        // Create payroll for employee
        document.querySelectorAll('.create-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const employeeId = e.target.closest('.create-payroll').dataset.employeeId;
                const month = e.target.closest('.create-payroll').dataset.month;
                this.createPayrollForEmployee(employeeId, month);
            });
        });

        // Delete payroll
        document.querySelectorAll('.delete-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.delete-payroll').dataset.payrollId;
                this.deletePayroll(payrollId);
            });
        });
    }

    calculateAllowances(payroll) {
        if (!payroll) return 0;

        const housing = parseFloat(payroll.housing_allowance) || 0;
        const transport = parseFloat(payroll.transport_allowance) || 0;
        const food = parseFloat(payroll.food_allowance) || 0;
        const other = parseFloat(payroll.other_allowances) || 0;

        return Math.round((housing + transport + food + other) * 100) / 100;
    }

    calculateBonuses(payroll) {
        if (!payroll) return 0;

        const performanceBonus = parseFloat(payroll.performance_bonus) || 0;
        const overtimeHours = parseFloat(payroll.overtime_hours) || 0;
        const otherBonuses = parseFloat(payroll.other_bonuses) || 0;

        // حساب قيمة الساعات الإضافية
        const basicSalary = parseFloat(payroll.basic_salary) || 0;
        const overtimePay = overtimeHours * this.getOvertimeRate(basicSalary);

        return Math.round((performanceBonus + overtimePay + otherBonuses) * 100) / 100;
    }

    getOvertimeRate(basicSalary) {
        if (!basicSalary || basicSalary <= 0) return 0;

        const settings = Database.getSettings();
        const workingDaysPerMonth = 22; // أيام العمل الفعلية في الشهر
        const hoursPerDay = 8; // ساعات العمل اليومية

        // حساب الأجر بالساعة
        const hourlyRate = basicSalary / (workingDaysPerMonth * hoursPerDay);

        // معدل الإضافي (عادة 1.5 من الأجر العادي)
        const overtimeMultiplier = settings.attendance?.overtime_rate || 1.5;

        return Math.round((hourlyRate * overtimeMultiplier) * 100) / 100;
    }

    getStatusBadge(status) {
        const statusMap = {
            'pending': {
                class: 'warning',
                text: 'في الانتظار',
                icon: 'fas fa-hourglass-half'
            },
            'paid': {
                class: 'success',
                text: 'مدفوعة',
                icon: 'fas fa-check-circle'
            },
            'cancelled': {
                class: 'danger',
                text: 'ملغية',
                icon: 'fas fa-times-circle'
            }
        };

        const statusInfo = statusMap[status] || {
            class: 'secondary',
            text: 'غير محدد',
            icon: 'fas fa-question-circle'
        };

        return `<span class="badge bg-${statusInfo.class} status-badge">
                    <i class="${statusInfo.icon} me-1"></i>
                    ${statusInfo.text}
                </span>`;
    }

    formatMonth(monthStr) {
        const [year, month] = monthStr.split('-');
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[parseInt(month) - 1]} ${year}`;
    }

    updatePayrollStats() {
        const payrolls = Database.getAll('payroll').filter(p => p.month === this.currentMonth);
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');

        // حساب الإجمالي بشكل صحيح ودقيق
        let totalPayroll = 0;
        let paidCount = 0;
        let pendingCount = 0;

        payrolls.forEach(p => {
            // استخدام دالة إعادة الحساب للحصول على قيم دقيقة
            const calculation = this.recalculatePayrollValues(p);
            totalPayroll += calculation.netSalary;

            if (p.status === 'paid') paidCount++;
            else if (p.status === 'pending') pendingCount++;
        });

        // تقريب الإجمالي النهائي
        totalPayroll = Math.round(totalPayroll * 100) / 100;

        // عدد الموظفين النشطين
        const employeeCount = employees.length;

        // عدد الموظفين الذين لديهم كشوف رواتب
        const payrollEmployeeCount = payrolls.length;

        // تحديث العرض
        document.getElementById('totalPayroll').textContent = window.samApp.formatCurrency(totalPayroll);
        document.getElementById('paidCount').textContent = paidCount;
        document.getElementById('pendingCount').textContent = pendingCount;
        document.getElementById('employeeCount').textContent = `${payrollEmployeeCount}/${employeeCount}`;
    }

    showPayrollModal(payroll = null) {
        const modal = new bootstrap.Modal(document.getElementById('payrollModal'));
        const form = document.getElementById('payrollForm');
        const title = document.getElementById('payrollModalTitle');

        if (payroll) {
            title.textContent = 'تعديل راتب';
            this.populatePayrollForm(form, payroll);
            this.selectedPayroll = payroll;
        } else {
            title.textContent = 'إضافة راتب';
            form.reset();
            form.querySelector('input[name="month"]').value = this.currentMonth;
            this.selectedPayroll = null;
        }

        modal.show();
    }

    populatePayrollForm(form, payroll) {
        Object.keys(payroll).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = payroll[key] || '';
            }
        });

        this.calculatePayroll();
    }

    loadEmployeeData(employeeId) {
        if (!employeeId) return;

        const employee = Database.getEmployee(employeeId);
        if (employee) {
            const form = document.getElementById('payrollForm');
            const month = form.querySelector('input[name="month"]').value;

            form.querySelector('input[name="basic_salary"]').value = employee.salary || 0;

            // Load attendance data for the month
            const attendance = Database.getAttendance({
                employee_id: employeeId,
                month: month
            });

            const workingDays = this.getWorkingDaysInMonth(month, employee);
            const attendanceDays = attendance.filter(a => a.status !== 'absent').length;

            form.querySelector('input[name="working_days"]').value = workingDays;
            form.querySelector('input[name="attendance_days"]').value = attendanceDays;

            // Load employee-specific deductions
            this.loadEmployeeDeductions(employeeId, month, form);

            this.calculatePayroll();
        }
    }

    loadEmployeeDeductions(employeeId, month, form) {
        // استخدام حاسبة الساعات الإضافية والخصومات
        if (!window.overtimeCalculator) {
            console.warn('OvertimeCalculator not available');
            return;
        }

        const calculations = window.overtimeCalculator.calculateAllowancesAndDeductions(employeeId, month);

        if (calculations) {
            // تحديث الإضافات بحذر
            const overtimeField = form.querySelector('input[name="overtime_hours"]');
            const transportField = form.querySelector('input[name="transport_allowance"]');
            const housingField = form.querySelector('input[name="housing_allowance"]');
            const otherAllowancesField = form.querySelector('input[name="other_allowances"]');

            if (overtimeField && calculations.details.overtime.hours > 0) {
                overtimeField.value = calculations.details.overtime.hours.toFixed(2);
            }
            if (transportField) {
                transportField.value = calculations.allowances.transport.toFixed(2);
            }
            if (housingField) {
                housingField.value = calculations.allowances.housing.toFixed(2);
            }
            if (otherAllowancesField) {
                otherAllowancesField.value = calculations.allowances.other.toFixed(2);
            }

            // تحديث الخصومات بحذر
            const advancesField = form.querySelector('input[name="advances"]');
            const latenessField = form.querySelector('input[name="lateness_deduction"]');
            const absenceField = form.querySelector('input[name="absence_deduction"]');
            const otherDeductionsField = form.querySelector('input[name="other_deductions"]');

            if (advancesField) {
                advancesField.value = calculations.deductions.advances.toFixed(2);
            }
            if (latenessField) {
                latenessField.value = calculations.deductions.lateness.toFixed(2);
            }
            if (absenceField) {
                absenceField.value = calculations.deductions.absence.toFixed(2);
            }
            if (otherDeductionsField) {
                otherDeductionsField.value = calculations.deductions.penalties.toFixed(2);
            }

            // حساب التأمينات الاجتماعية وضريبة الدخل
            const employee = Database.getEmployee(employeeId);
            if (employee) {
                const basicSalary = employee.salary || 0;
                const socialInsuranceField = form.querySelector('input[name="social_insurance"]');
                const incomeTaxField = form.querySelector('input[name="income_tax"]');

                if (socialInsuranceField) {
                    const socialInsurance = basicSalary * 0.09; // 9%
                    socialInsuranceField.value = socialInsurance.toFixed(2);
                }

                if (incomeTaxField) {
                    const incomeTax = this.calculateIncomeTax(basicSalary);
                    incomeTaxField.value = incomeTax.toFixed(2);
                }
            }

            // حفظ التفاصيل للعرض في مفردات الراتب
            this.payrollDetails = calculations.details;
        }
    }

    getWorkingDaysInMonth(monthStr, employee = null) {
        const [year, month] = monthStr.split('-');
        const date = new Date(year, month - 1, 1);
        const lastDay = new Date(year, month, 0).getDate();
        let workingDays = 0;

        // Use employee-specific working days if available, otherwise use global settings
        let workingDaysList;
        if (employee && employee.working_days && employee.working_days.length > 0) {
            workingDaysList = employee.working_days;
        } else {
            const settings = Database.getSettings();
            workingDaysList = settings.working_hours.working_days || ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
        }

        for (let day = 1; day <= lastDay; day++) {
            date.setDate(day);
            const dayName = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
            if (workingDaysList.includes(dayName)) {
                workingDays++;
            }
        }

        return workingDays;
    }

    calculatePayroll() {
        const form = document.getElementById('payrollForm');
        if (!form) return;

        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // التحقق من البيانات الأساسية
        const basicSalary = parseFloat(data.basic_salary) || 0;
        const workingDays = parseFloat(data.working_days) || 22; // افتراضي 22 يوم عمل
        const attendanceDays = parseFloat(data.attendance_days) || 0;

        // حساب الراتب النسبي بناءً على أيام الحضور
        const proportionalSalary = workingDays > 0 ?
            Math.round((basicSalary * attendanceDays / workingDays) * 100) / 100 : 0;

        // حساب البدلات (مجموع واحد)
        const housingAllowance = parseFloat(data.housing_allowance) || 0;
        const transportAllowance = parseFloat(data.transport_allowance) || 0;
        const foodAllowance = parseFloat(data.food_allowance) || 0;
        const otherAllowances = parseFloat(data.other_allowances) || 0;
        const totalAllowances = Math.round((housingAllowance + transportAllowance + foodAllowance + otherAllowances) * 100) / 100;

        // حساب الحوافز والمكافآت (مجموع واحد)
        const performanceBonus = parseFloat(data.performance_bonus) || 0;
        const overtimeHours = parseFloat(data.overtime_hours) || 0;
        const otherBonuses = parseFloat(data.other_bonuses) || 0;

        // حساب قيمة الساعات الإضافية
        const overtimePay = Math.round((overtimeHours * this.getOvertimeRate(basicSalary)) * 100) / 100;
        const totalBonuses = Math.round((performanceBonus + overtimePay + otherBonuses) * 100) / 100;

        // حساب إجمالي الاستحقاقات (رقم واحد نهائي)
        const grossSalary = Math.round((proportionalSalary + totalAllowances + totalBonuses) * 100) / 100;

        // حساب الخصومات (مجموع واحد)
        const socialInsurance = parseFloat(data.social_insurance) || 0;
        const incomeTax = parseFloat(data.income_tax) || 0;
        const advances = parseFloat(data.advances) || 0;
        const latenessDeduction = parseFloat(data.lateness_deduction) || 0;
        const absenceDeduction = parseFloat(data.absence_deduction) || 0;
        const otherDeductions = parseFloat(data.other_deductions) || 0;

        const totalDeductions = Math.round((socialInsurance + incomeTax + advances + latenessDeduction + absenceDeduction + otherDeductions) * 100) / 100;

        // حساب صافي الراتب (رقم واحد نهائي)
        const netSalary = Math.max(0, Math.round((grossSalary - totalDeductions) * 100) / 100);

        // تحديث الحقول في النموذج
        const grossSalaryField = form.querySelector('input[name="gross_salary"]');
        const totalDeductionsField = form.querySelector('input[name="total_deductions"]');
        const netSalaryField = form.querySelector('input[name="net_salary"]');

        if (grossSalaryField) grossSalaryField.value = grossSalary.toFixed(2);
        if (totalDeductionsField) totalDeductionsField.value = totalDeductions.toFixed(2);
        if (netSalaryField) netSalaryField.value = netSalary.toFixed(2);

        // حفظ النتائج المحسوبة مع تفاصيل دقيقة
        this.lastCalculation = {
            basicSalary: basicSalary,
            workingDays: workingDays,
            attendanceDays: attendanceDays,
            proportionalSalary: proportionalSalary,

            // تفاصيل البدلات
            housingAllowance: housingAllowance,
            transportAllowance: transportAllowance,
            foodAllowance: foodAllowance,
            otherAllowances: otherAllowances,
            totalAllowances: totalAllowances,

            // تفاصيل الحوافز
            performanceBonus: performanceBonus,
            overtimeHours: overtimeHours,
            overtimePay: overtimePay,
            otherBonuses: otherBonuses,
            totalBonuses: totalBonuses,

            // تفاصيل الخصومات
            socialInsurance: socialInsurance,
            incomeTax: incomeTax,
            advances: advances,
            latenessDeduction: latenessDeduction,
            absenceDeduction: absenceDeduction,
            otherDeductions: otherDeductions,
            totalDeductions: totalDeductions,

            // النتائج النهائية
            grossSalary: grossSalary,
            netSalary: netSalary
        };

        return this.lastCalculation;
    }

    savePayroll() {
        const form = document.getElementById('payrollForm');
        if (!form) {
            window.samApp.showAlert('خطأ في النموذج', 'danger');
            return;
        }

        // Validate required fields
        const employeeId = form.querySelector('select[name="employee_id"]').value;
        const month = form.querySelector('input[name="month"]').value;

        if (!employeeId || !month) {
            window.samApp.showAlert('يرجى تحديد الموظف والشهر', 'warning');
            return;
        }

        // Check for duplicate payroll
        if (!this.selectedPayroll) {
            const existingPayroll = Database.getAll('payroll').find(p =>
                p.employee_id === employeeId && p.month === month
            );
            if (existingPayroll) {
                window.samApp.showAlert('يوجد كشف راتب لهذا الموظف في نفس الشهر', 'warning');
                return;
            }
        }

        const formData = new FormData(form);
        const payrollData = Object.fromEntries(formData.entries());

        // Convert numeric fields with validation
        const numericFields = ['basic_salary', 'working_days', 'attendance_days', 'housing_allowance',
                              'transport_allowance', 'food_allowance', 'other_allowances', 'performance_bonus',
                              'overtime_hours', 'other_bonuses', 'social_insurance', 'income_tax', 'advances',
                              'lateness_deduction', 'absence_deduction', 'other_deductions',
                              'gross_salary', 'total_deductions', 'net_salary'];

        numericFields.forEach(field => {
            const value = parseFloat(payrollData[field]);
            payrollData[field] = isNaN(value) ? 0 : value;
        });

        // Ensure calculations are correct before saving
        this.calculatePayroll();

        // Update with latest calculated values
        if (this.lastCalculation) {
            payrollData.gross_salary = this.lastCalculation.grossSalary;
            payrollData.total_deductions = this.lastCalculation.totalDeductions;
            payrollData.net_salary = this.lastCalculation.netSalary;
        }

        try {
            // إظهار مؤشر التحميل
            const saveBtn = document.getElementById('savePayrollBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            saveBtn.disabled = true;

            if (this.selectedPayroll) {
                // Update existing payroll
                payrollData.updated_at = new Date().toISOString();
                Database.update('payroll', this.selectedPayroll.id, payrollData);

                // إشعار مفصل
                const employee = Database.getEmployee(payrollData.employee_id);
                window.samApp.showAlert(
                    `تم تحديث كشف راتب ${employee?.name || 'الموظف'} لشهر ${this.formatMonth(payrollData.month)} بنجاح`,
                    'success'
                );
            } else {
                // Create new payroll
                payrollData.created_at = new Date().toISOString();
                payrollData.updated_at = new Date().toISOString();
                Database.create('payroll', payrollData);

                // إشعار مفصل
                const employee = Database.getEmployee(payrollData.employee_id);
                window.samApp.showAlert(
                    `تم إضافة كشف راتب ${employee?.name || 'الموظف'} لشهر ${this.formatMonth(payrollData.month)} بنجاح`,
                    'success'
                );
            }

            // إخفاء النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('payrollModal'));
            if (modal) modal.hide();

            // تحديث البيانات
            this.loadPayrollData();

            // إعادة تعيين الزر
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;

        } catch (error) {
            console.error('Error saving payroll:', error);
            window.samApp.showAlert('حدث خطأ أثناء حفظ البيانات: ' + error.message, 'danger');

            // إعادة تعيين الزر في حالة الخطأ
            const saveBtn = document.getElementById('savePayrollBtn');
            saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ';
            saveBtn.disabled = false;
        }
    }

    generateMonthlyPayroll() {
        const month = prompt('أدخل الشهر (YYYY-MM):', this.currentMonth);
        if (!month) return;

        // Validate month format
        if (!/^\d{4}-\d{2}$/.test(month)) {
            window.samApp.showAlert('تنسيق الشهر غير صحيح. يجب أن يكون بالتنسيق YYYY-MM', 'danger');
            return;
        }

        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        if (employees.length === 0) {
            window.samApp.showAlert('لا يوجد موظفون نشطون لإنشاء كشوف رواتب', 'warning');
            return;
        }

        const existingPayrolls = Database.getAll('payroll').filter(p => p.month === month);

        if (existingPayrolls.length > 0) {
            if (!confirm(`يوجد ${existingPayrolls.length} كشف راتب لهذا الشهر. هل تريد المتابعة وإنشاء كشوف للموظفين المتبقين؟`)) {
                return;
            }
        }

        let generatedCount = 0;
        let skippedCount = 0;
        let errorCount = 0;

        employees.forEach(employee => {
            try {
                // Check if payroll already exists for this employee and month
                const existing = existingPayrolls.find(p => p.employee_id === employee.id);
                if (existing) {
                    skippedCount++;
                    return;
                }

                // Validate employee data
                if (!employee.salary || employee.salary <= 0) {
                    console.warn(`Employee ${employee.name} has no valid salary`);
                    errorCount++;
                    return;
                }

                // Get attendance data
                const attendance = Database.getAttendance({ employee_id: employee.id, month });
                const workingDays = this.getWorkingDaysInMonth(month, employee);
                const attendanceDays = attendance.filter(a => a.status !== 'absent').length;

                // Calculate basic payroll data
                const basicSalary = employee.salary || 0;
                const proportionalSalary = workingDays > 0 ? (basicSalary * attendanceDays / workingDays) : basicSalary;

                // Get employee-specific allowances and deductions
                let calculations = null;
                if (window.overtimeCalculator) {
                    calculations = window.overtimeCalculator.calculateAllowancesAndDeductions(employee.id, month);
                }

                // Basic deductions
                const socialInsurance = basicSalary * 0.09; // 9% social insurance
                const incomeTax = this.calculateIncomeTax(basicSalary);

                // Calculate allowances and bonuses separately
                const housingAllowance = calculations?.allowances.housing || 0;
                const transportAllowance = calculations?.allowances.transport || 0;
                const otherAllowances = calculations?.allowances.other || 0;
                const overtimeHours = calculations?.details.overtime.hours || 0;

                // Calculate deductions
                const advancesDeduction = calculations?.deductions.advances || 0;
                const latenessDeduction = calculations?.deductions.lateness || 0;
                const absenceDeduction = calculations?.deductions.absence || 0;
                const penaltiesDeduction = calculations?.deductions.penalties || 0;

                // Calculate totals
                const totalAllowances = housingAllowance + transportAllowance + otherAllowances;
                const overtimePay = overtimeHours * this.getOvertimeRate(basicSalary);
                const grossSalary = proportionalSalary + totalAllowances + overtimePay;
                const totalDeductions = socialInsurance + incomeTax + advancesDeduction + latenessDeduction + absenceDeduction + penaltiesDeduction;
                const netSalary = Math.max(0, grossSalary - totalDeductions);

                const payrollData = {
                    employee_id: employee.id,
                    month: month,
                    basic_salary: basicSalary,
                    working_days: workingDays,
                    attendance_days: attendanceDays,
                    housing_allowance: housingAllowance,
                    transport_allowance: transportAllowance,
                    food_allowance: 0,
                    other_allowances: otherAllowances,
                    performance_bonus: 0,
                    overtime_hours: overtimeHours,
                    other_bonuses: 0,
                    social_insurance: socialInsurance,
                    income_tax: incomeTax,
                    advances: advancesDeduction,
                    lateness_deduction: latenessDeduction,
                    absence_deduction: absenceDeduction,
                    other_deductions: penaltiesDeduction,
                    gross_salary: Math.round(grossSalary * 100) / 100,
                    total_deductions: Math.round(totalDeductions * 100) / 100,
                    net_salary: Math.round(netSalary * 100) / 100,
                    status: 'pending',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                Database.create('payroll', payrollData);
                generatedCount++;
            } catch (error) {
                console.error(`Error generating payroll for employee ${employee.name}:`, error);
                errorCount++;
            }
        });

        // Show summary message
        let message = `تم إنشاء ${generatedCount} كشف راتب بنجاح`;
        if (skippedCount > 0) {
            message += `، تم تخطي ${skippedCount} موظف (يوجد كشف راتب مسبقاً)`;
        }
        if (errorCount > 0) {
            message += `، فشل في إنشاء ${errorCount} كشف راتب`;
        }

        const alertType = errorCount > 0 ? 'warning' : 'success';
        window.samApp.showAlert(message, alertType);

        this.loadPayrollData();
    }

    calculateIncomeTax(salary) {
        // Simple tax calculation - can be customized based on local tax laws
        if (salary <= 3000) return 0;
        if (salary <= 5000) return salary * 0.05;
        if (salary <= 10000) return salary * 0.10;
        return salary * 0.15;
    }

    viewPayroll(payrollId) {
        const payroll = Database.getById('payroll', payrollId);
        if (!payroll) {
            window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
            return;
        }

        // إعادة حساب القيم للتأكد من الدقة
        const calculation = this.recalculatePayrollValues(payroll);

        // تحديث كشف الراتب بالقيم المحسوبة الصحيحة
        const updatedPayroll = {
            ...payroll,
            gross_salary: calculation.grossSalary,
            total_deductions: calculation.totalDeductions,
            net_salary: calculation.netSalary
        };

        this.showPayrollModal(updatedPayroll);

        // إعادة حساب القيم في النموذج
        setTimeout(() => {
            this.calculatePayroll();
        }, 100);
    }

    markAsPaid(payrollId) {
        try {
            const payroll = Database.getById('payroll', payrollId);
            if (!payroll) {
                window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
                return;
            }

            const updatedData = {
                ...payroll,
                status: 'paid',
                payment_date: new Date().toISOString().split('T')[0],
                updated_at: new Date().toISOString()
            };

            Database.update('payroll', payrollId, updatedData);
            window.samApp.showAlert('تم تحديد الراتب كمدفوع بنجاح', 'success');
            this.loadPayrollData();
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    deletePayroll(payrollId) {
        const payroll = Database.getById('payroll', payrollId);
        if (!payroll) {
            window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
            return;
        }

        const employee = Database.getEmployee(payroll.employee_id);
        const employeeName = employee?.name || 'الموظف';
        const monthName = this.formatMonth(payroll.month);

        if (confirm(`هل أنت متأكد من حذف كشف راتب ${employeeName} لشهر ${monthName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            try {
                Database.delete('payroll', payrollId);
                window.samApp.showAlert(`تم حذف كشف راتب ${employeeName} بنجاح`, 'success');
                this.loadPayrollData();
            } catch (error) {
                console.error('Error deleting payroll:', error);
                window.samApp.showAlert('حدث خطأ أثناء حذف كشف الراتب: ' + error.message, 'danger');
            }
        }
    }

    printPayslip(payrollId) {
        const payroll = Database.getById('payroll', payrollId);
        const employee = Database.getEmployee(payroll.employee_id);
        const settings = Database.getSettings();

        if (!payroll || !employee) {
            window.samApp.showAlert('البيانات غير متوفرة', 'danger');
            return;
        }

        // Create payslip HTML
        const payslipHTML = this.generatePayslipHTML(payroll, employee, settings);

        // Open print window
        const printWindow = window.open('', '_blank');
        printWindow.document.write(payslipHTML);
        printWindow.document.close();
        printWindow.print();
    }

    generatePayslipHTML(payroll, employee, settings) {
        // إعادة حساب القيم للتأكد من الدقة
        const calculation = this.recalculatePayrollValues(payroll);

        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>قسيمة راتب - ${employee.name}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
                    .employee-info { margin: 20px 0; }
                    .payroll-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .payroll-table th, .payroll-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    .payroll-table th { background-color: #f5f5f5; }
                    .total-row { font-weight: bold; background-color: #e9ecef; }
                    .summary-section { background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${settings.company?.name || 'الشركة'}</h1>
                    <h2>قسيمة راتب</h2>
                    <p>شهر: ${this.formatMonth(payroll.month)}</p>
                </div>

                <div class="employee-info">
                    <p><strong>اسم الموظف:</strong> ${employee.name}</p>
                    <p><strong>الرقم الوظيفي:</strong> ${employee.employee_number || 'غير محدد'}</p>
                    <p><strong>القسم:</strong> ${employee.department || 'غير محدد'}</p>
                    <p><strong>أيام العمل:</strong> ${calculation.workingDays} يوم</p>
                    <p><strong>أيام الحضور:</strong> ${calculation.attendanceDays} يوم</p>
                </div>

                <div class="summary-section">
                    <h3 style="color: #007bff; margin-bottom: 15px;">ملخص الراتب</h3>
                    <table style="width: 100%; border: none;">
                        <tr>
                            <td style="border: none; padding: 5px;"><strong>الراتب الأساسي:</strong></td>
                            <td style="border: none; padding: 5px; text-align: left;">${window.samApp.formatCurrency(calculation.basicSalary)}</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px;"><strong>إجمالي البدلات:</strong></td>
                            <td style="border: none; padding: 5px; text-align: left; color: #28a745;">${window.samApp.formatCurrency(calculation.totalAllowances)}</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px;"><strong>إجمالي الحوافز:</strong></td>
                            <td style="border: none; padding: 5px; text-align: left; color: #17a2b8;">${window.samApp.formatCurrency(calculation.totalBonuses)}</td>
                        </tr>
                        <tr style="border-top: 2px solid #007bff;">
                            <td style="border: none; padding: 10px 5px;"><strong>إجمالي الاستحقاقات:</strong></td>
                            <td style="border: none; padding: 10px 5px; text-align: left; font-size: 18px; color: #007bff;"><strong>${window.samApp.formatCurrency(calculation.grossSalary)}</strong></td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px;"><strong>إجمالي الخصومات:</strong></td>
                            <td style="border: none; padding: 5px; text-align: left; color: #dc3545;">(${window.samApp.formatCurrency(calculation.totalDeductions)})</td>
                        </tr>
                        <tr style="border-top: 2px solid #28a745; background-color: #d4edda;">
                            <td style="border: none; padding: 15px 5px;"><strong style="font-size: 18px;">صافي الراتب:</strong></td>
                            <td style="border: none; padding: 15px 5px; text-align: left; font-size: 20px; color: #28a745;"><strong>${window.samApp.formatCurrency(calculation.netSalary)}</strong></td>
                        </tr>
                    </table>
                </div>

                <table class="payroll-table">
                    <tr><th colspan="2" style="background-color: #007bff; color: white;">تفصيل الاستحقاقات</th></tr>
                    <tr><td>الراتب الأساسي (${calculation.attendanceDays}/${calculation.workingDays} يوم)</td><td>${window.samApp.formatCurrency(calculation.proportionalSalary)}</td></tr>
                    ${calculation.housingAllowance > 0 ? `<tr><td>بدل سكن</td><td>${window.samApp.formatCurrency(calculation.housingAllowance)}</td></tr>` : ''}
                    ${calculation.transportAllowance > 0 ? `<tr><td>بدل مواصلات</td><td>${window.samApp.formatCurrency(calculation.transportAllowance)}</td></tr>` : ''}
                    ${calculation.foodAllowance > 0 ? `<tr><td>بدل طعام</td><td>${window.samApp.formatCurrency(calculation.foodAllowance)}</td></tr>` : ''}
                    ${calculation.otherAllowances > 0 ? `<tr><td>بدلات أخرى</td><td>${window.samApp.formatCurrency(calculation.otherAllowances)}</td></tr>` : ''}
                    ${calculation.overtimePay > 0 ? `<tr><td>ساعات إضافية (${calculation.overtimeHours} ساعة)</td><td>${window.samApp.formatCurrency(calculation.overtimePay)}</td></tr>` : ''}
                    ${calculation.performanceBonus > 0 ? `<tr><td>حافز الأداء</td><td>${window.samApp.formatCurrency(calculation.performanceBonus)}</td></tr>` : ''}
                    ${calculation.otherBonuses > 0 ? `<tr><td>حوافز أخرى</td><td>${window.samApp.formatCurrency(calculation.otherBonuses)}</td></tr>` : ''}

                    <tr><th colspan="2" style="background-color: #dc3545; color: white;">تفصيل الخصومات</th></tr>
                    ${calculation.socialInsurance > 0 ? `<tr><td>التأمينات الاجتماعية</td><td>(${window.samApp.formatCurrency(calculation.socialInsurance)})</td></tr>` : ''}
                    ${calculation.incomeTax > 0 ? `<tr><td>ضريبة الدخل</td><td>(${window.samApp.formatCurrency(calculation.incomeTax)})</td></tr>` : ''}
                    ${calculation.advances > 0 ? `<tr><td>السلف</td><td>(${window.samApp.formatCurrency(calculation.advances)})</td></tr>` : ''}
                    ${calculation.latenessDeduction > 0 ? `<tr><td>خصم التأخير</td><td>(${window.samApp.formatCurrency(calculation.latenessDeduction)})</td></tr>` : ''}
                    ${calculation.absenceDeduction > 0 ? `<tr><td>خصم الغياب</td><td>(${window.samApp.formatCurrency(calculation.absenceDeduction)})</td></tr>` : ''}
                    ${calculation.otherDeductions > 0 ? `<tr><td>خصومات أخرى</td><td>(${window.samApp.formatCurrency(calculation.otherDeductions)})</td></tr>` : ''}
                </table>

                <div style="margin-top: 40px; text-align: center; border-top: 1px solid #ddd; padding-top: 20px;">
                    <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p style="font-size: 12px; color: #666;">تم إنشاء هذه القسيمة تلقائياً بواسطة نظام إدارة شؤون الموظفين</p>
                </div>
            </body>
            </html>
        `;
    }

    generateDetailedBreakdown(details) {
        return `
            <div style="margin-top: 30px; page-break-inside: avoid;">
                <h3 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 5px;">تفصيل مفردات الراتب</h3>

                ${details.overtime.hours > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f8f9fa; border-right: 4px solid #28a745;">
                    <h4 style="color: #28a745; margin-bottom: 10px;">الساعات الإضافية</h4>
                    <p><strong>عدد الساعات:</strong> ${details.overtime.hours} ساعة</p>
                    <p><strong>عدد الأيام:</strong> ${details.overtime.days} يوم</p>
                    <p><strong>المبلغ:</strong> ${window.samApp.formatCurrency(details.overtime.amount)}</p>
                </div>
                ` : ''}

                ${details.undertime.hours > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #fff3cd; border-right: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">نقص في ساعات العمل</h4>
                    <p><strong>عدد الساعات:</strong> ${details.undertime.hours} ساعة</p>
                    <p><strong>عدد الأيام:</strong> ${details.undertime.days} يوم</p>
                    <p><strong>المبلغ المخصوم:</strong> ${window.samApp.formatCurrency(details.undertime.amount)}</p>
                </div>
                ` : ''}

                ${details.lateness.days > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f8d7da; border-right: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin-bottom: 10px;">التأخير</h4>
                    <p><strong>إجمالي دقائق التأخير:</strong> ${details.lateness.minutes} دقيقة</p>
                    <p><strong>عدد أيام التأخير:</strong> ${details.lateness.days} يوم</p>
                    <p><strong>المبلغ المخصوم:</strong> ${window.samApp.formatCurrency(details.lateness.amount)}</p>
                </div>
                ` : ''}

                ${details.absence.days > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f8d7da; border-right: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin-bottom: 10px;">الغياب</h4>
                    <p><strong>عدد أيام الغياب:</strong> ${details.absence.days} يوم</p>
                    <p><strong>المبلغ المخصوم:</strong> ${window.samApp.formatCurrency(details.absence.amount)}</p>
                </div>
                ` : ''}

                ${details.advances.count > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #e2e3e5; border-right: 4px solid #6c757d;">
                    <h4 style="color: #495057; margin-bottom: 10px;">السلف</h4>
                    <p><strong>عدد السلف النشطة:</strong> ${details.advances.count}</p>
                    <p><strong>إجمالي الأقساط الشهرية:</strong> ${window.samApp.formatCurrency(details.advances.amount)}</p>
                </div>
                ` : ''}

                ${details.penalties.count > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f8d7da; border-right: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin-bottom: 10px;">الجزاءات</h4>
                    <p><strong>عدد الجزاءات:</strong> ${details.penalties.count}</p>
                    <p><strong>إجمالي المبلغ:</strong> ${window.samApp.formatCurrency(details.penalties.amount)}</p>
                </div>
                ` : ''}
            </div>
        `;
    }

    exportPayroll() {
        const payrolls = Database.getAll('payroll').filter(p => p.month === this.currentMonth);
        const employees = Database.getEmployees();

        const exportData = payrolls.map(payroll => {
            const employee = employees.find(emp => emp.id === payroll.employee_id);
            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'الشهر': this.formatMonth(payroll.month),
                'الراتب الأساسي': payroll.basic_salary,
                'البدلات': this.calculateAllowances(payroll),
                'الحوافز': this.calculateBonuses(payroll),
                'إجمالي الاستحقاقات': payroll.gross_salary,
                'الخصومات': payroll.total_deductions,
                'صافي الراتب': payroll.net_salary,
                'الحالة': payroll.status,
                'تاريخ الدفع': payroll.payment_date || '',
                'ملاحظات': payroll.notes || ''
            };
        });

        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الرواتب');
        XLSX.writeFile(wb, `payroll_${this.currentMonth}.xlsx`);

        window.samApp.showAlert('تم تصدير بيانات الرواتب بنجاح', 'success');
    }

    printPayrollSummary() {
        const payrolls = Database.getAll('payroll') || [];
        const monthPayrolls = payrolls.filter(p => p.month === this.currentMonth);

        if (monthPayrolls.length === 0) {
            window.samApp.showAlert('لا توجد كشوف رواتب للشهر المحدد', 'warning');
            return;
        }

        window.printManager.printPayrollReport(monthPayrolls, this.currentMonth);
    }

    // تنظيف الموارد عند مغادرة الصفحة
    cleanup() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    // دالة لإعادة حساب جميع كشوف الرواتب في الشهر المحدد
    recalculateMonthlyPayrolls() {
        const payrolls = Database.getAll('payroll').filter(p => p.month === this.currentMonth);
        let updatedCount = 0;

        payrolls.forEach(payroll => {
            const employee = Database.getEmployee(payroll.employee_id);
            if (!employee) return;

            // إعادة حساب البيانات
            const attendance = Database.getAttendance({
                employee_id: payroll.employee_id,
                month: payroll.month
            });

            const workingDays = this.getWorkingDaysInMonth(payroll.month, employee);
            const attendanceDays = attendance.filter(a => a.status !== 'absent').length;

            // تحديث البيانات المحسوبة
            const basicSalary = employee.salary || 0;
            const proportionalSalary = workingDays > 0 ? (basicSalary * attendanceDays / workingDays) : basicSalary;

            const updatedData = {
                ...payroll,
                basic_salary: basicSalary,
                working_days: workingDays,
                attendance_days: attendanceDays,
                updated_at: new Date().toISOString()
            };

            // إعادة حساب الإجمالي والصافي
            const allowances = this.calculateAllowances(updatedData);
            const bonuses = this.calculateBonuses(updatedData);
            const grossSalary = proportionalSalary + allowances + bonuses;

            const totalDeductions = (updatedData.social_insurance || 0) +
                                  (updatedData.income_tax || 0) +
                                  (updatedData.advances || 0) +
                                  (updatedData.lateness_deduction || 0) +
                                  (updatedData.absence_deduction || 0) +
                                  (updatedData.other_deductions || 0);

            updatedData.gross_salary = grossSalary;
            updatedData.total_deductions = totalDeductions;
            updatedData.net_salary = Math.max(0, grossSalary - totalDeductions);

            Database.update('payroll', payroll.id, updatedData);
            updatedCount++;
        });

        if (updatedCount > 0) {
            window.samApp.showAlert(`تم إعادة حساب ${updatedCount} كشف راتب بنجاح`, 'success');
            this.loadPayrollData();
        } else {
            window.samApp.showAlert('لا توجد كشوف رواتب للتحديث', 'info');
        }
    }

    // إنشاء كشف راتب لموظف محدد
    createPayrollForEmployee(employeeId, month) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) {
            window.samApp.showAlert('الموظف غير موجود', 'danger');
            return;
        }

        // فتح نموذج إضافة راتب مع بيانات الموظف
        this.showPayrollModal();

        // ملء البيانات الأساسية
        const form = document.getElementById('payrollForm');
        form.querySelector('select[name="employee_id"]').value = employeeId;
        form.querySelector('input[name="month"]').value = month;

        // تحميل بيانات الموظف
        this.loadEmployeeData(employeeId);
    }

    // إنشاء سجل مؤقت للموظف بدون كشف راتب
    createTempPayrollRecord(employee, month) {
        // حساب البيانات الأساسية بناءً على الحضور
        const attendanceData = this.calculateEmployeeAttendanceData(employee.id, month);
        const basicSalary = employee.salary || 0;

        // حساب التأمينات والضرائب الأساسية
        const socialInsurance = Math.round(basicSalary * 0.09 * 100) / 100; // 9%
        const incomeTax = this.calculateIncomeTax(basicSalary);

        return {
            id: `temp_${employee.id}_${month}`,
            employee_id: employee.id,
            month: month,
            basic_salary: basicSalary,
            working_days: attendanceData.workingDays,
            attendance_days: attendanceData.attendanceDays,
            housing_allowance: 0,
            transport_allowance: 0,
            food_allowance: 0,
            other_allowances: 0,
            performance_bonus: 0,
            overtime_hours: attendanceData.overtimeHours,
            other_bonuses: 0,
            social_insurance: socialInsurance,
            income_tax: incomeTax,
            advances: 0,
            lateness_deduction: attendanceData.latenessDeduction,
            absence_deduction: attendanceData.absenceDeduction,
            other_deductions: 0,
            gross_salary: attendanceData.proportionalSalary,
            total_deductions: socialInsurance + incomeTax + attendanceData.latenessDeduction + attendanceData.absenceDeduction,
            net_salary: Math.max(0, attendanceData.proportionalSalary - (socialInsurance + incomeTax + attendanceData.latenessDeduction + attendanceData.absenceDeduction)),
            status: 'not_created',
            isTemporary: true,
            created_at: new Date().toISOString()
        };
    }

    // حساب بيانات الحضور للموظف
    calculateEmployeeAttendanceData(employeeId, month) {
        const attendance = Database.getAll('attendance').filter(a =>
            a.employee_id === employeeId && a.date.startsWith(month)
        );

        const workingDays = this.getWorkingDaysInMonth(month);
        const presentDays = attendance.filter(a => a.status === 'present' || a.status === 'late').length;
        const lateDays = attendance.filter(a => a.status === 'late').length;
        const absentDays = workingDays - presentDays;

        // حساب الساعات الإضافية
        const overtimeHours = attendance.reduce((total, a) => {
            if (a.overtime_hours) {
                return total + parseFloat(a.overtime_hours);
            }
            return total;
        }, 0);

        const employee = Database.getEmployee(employeeId);
        const basicSalary = employee?.salary || 0;
        const dailySalary = basicSalary / workingDays;

        // حساب الراتب النسبي
        const proportionalSalary = Math.round(dailySalary * presentDays * 100) / 100;

        // حساب خصم التأخير (إذا كان هناك إعداد لذلك)
        const settings = Database.getSettings();
        const lateDeductionPerDay = settings.payroll?.late_deduction_per_day || 0;
        const latenessDeduction = Math.round(lateDays * lateDeductionPerDay * 100) / 100;

        // حساب خصم الغياب
        const absenceDeduction = Math.round(absentDays * dailySalary * 100) / 100;

        return {
            workingDays,
            attendanceDays: presentDays,
            absentDays,
            lateDays,
            overtimeHours,
            proportionalSalary,
            latenessDeduction,
            absenceDeduction
        };
    }

    // إعادة حساب قيم كشف الراتب للتأكد من الدقة
    recalculatePayrollValues(payroll) {
        const basicSalary = parseFloat(payroll.basic_salary) || 0;
        const workingDays = parseFloat(payroll.working_days) || 22;
        const attendanceDays = parseFloat(payroll.attendance_days) || 0;

        // حساب الراتب النسبي
        const proportionalSalary = workingDays > 0 ?
            Math.round((basicSalary * attendanceDays / workingDays) * 100) / 100 : 0;

        // البدلات
        const housingAllowance = parseFloat(payroll.housing_allowance) || 0;
        const transportAllowance = parseFloat(payroll.transport_allowance) || 0;
        const foodAllowance = parseFloat(payroll.food_allowance) || 0;
        const otherAllowances = parseFloat(payroll.other_allowances) || 0;
        const totalAllowances = Math.round((housingAllowance + transportAllowance + foodAllowance + otherAllowances) * 100) / 100;

        // الحوافز
        const performanceBonus = parseFloat(payroll.performance_bonus) || 0;
        const overtimeHours = parseFloat(payroll.overtime_hours) || 0;
        const otherBonuses = parseFloat(payroll.other_bonuses) || 0;
        const overtimePay = Math.round((overtimeHours * this.getOvertimeRate(basicSalary)) * 100) / 100;
        const totalBonuses = Math.round((performanceBonus + overtimePay + otherBonuses) * 100) / 100;

        // إجمالي الاستحقاقات
        const grossSalary = Math.round((proportionalSalary + totalAllowances + totalBonuses) * 100) / 100;

        // الخصومات
        const socialInsurance = parseFloat(payroll.social_insurance) || 0;
        const incomeTax = parseFloat(payroll.income_tax) || 0;
        const advances = parseFloat(payroll.advances) || 0;
        const latenessDeduction = parseFloat(payroll.lateness_deduction) || 0;
        const absenceDeduction = parseFloat(payroll.absence_deduction) || 0;
        const otherDeductions = parseFloat(payroll.other_deductions) || 0;
        const totalDeductions = Math.round((socialInsurance + incomeTax + advances + latenessDeduction + absenceDeduction + otherDeductions) * 100) / 100;

        // صافي الراتب
        const netSalary = Math.max(0, Math.round((grossSalary - totalDeductions) * 100) / 100);

        return {
            basicSalary,
            workingDays,
            attendanceDays,
            proportionalSalary,
            housingAllowance,
            transportAllowance,
            foodAllowance,
            otherAllowances,
            totalAllowances,
            performanceBonus,
            overtimeHours,
            overtimePay,
            otherBonuses,
            totalBonuses,
            socialInsurance,
            incomeTax,
            advances,
            latenessDeduction,
            absenceDeduction,
            otherDeductions,
            totalDeductions,
            grossSalary,
            netSalary
        };
    }
}