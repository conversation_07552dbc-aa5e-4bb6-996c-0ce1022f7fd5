/**
 * SAM - نظام إدارة شؤون الموظفين
 * Main Application JavaScript
 * المطور: MOHANNAD AHMAD
 */

class SAMApp {
    constructor() {
        this.currentUser = null;
        this.currentSection = 'dashboard';
        this.currentManager = null; // لتتبع المدير الحالي للصفحة
        this.init();
    }

    init() {
        // Initialize the application
        this.showLoadingScreen();
        
        // Simulate loading time
        setTimeout(() => {
            this.hideLoadingScreen();
            this.checkAuthentication();
        }, 2000);

        // Bind events
        this.bindEvents();
    }

    bindEvents() {
        // Navigation events
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-section]')) {
                e.preventDefault();
                const section = e.target.getAttribute('data-section');
                this.navigateToSection(section);
            }
        });

        // Logout event
        document.getElementById('logoutBtn')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.logout();
        });

        // Window resize event
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    showLoadingScreen() {
        document.getElementById('loadingScreen').classList.remove('d-none');
        document.getElementById('loginScreen').classList.add('d-none');
        document.getElementById('mainApp').classList.add('d-none');
    }

    hideLoadingScreen() {
        document.getElementById('loadingScreen').classList.add('d-none');
    }

    showLoginScreen() {
        document.getElementById('loginScreen').classList.remove('d-none');
        document.getElementById('mainApp').classList.add('d-none');
    }

    showMainApp() {
        document.getElementById('loginScreen').classList.add('d-none');
        document.getElementById('mainApp').classList.remove('d-none');
        this.loadDashboard();
    }

    checkAuthentication() {
        const savedUser = localStorage.getItem('sam_current_user');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
            this.showMainApp();
            this.updateUserDisplay();
        } else {
            this.showLoginScreen();
        }
    }

    updateUserDisplay() {
        const userElement = document.getElementById('currentUser');
        if (userElement && this.currentUser) {
            userElement.textContent = this.currentUser.name || this.currentUser.username;
        }
    }

    navigateToSection(section) {
        // Update active navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        document.querySelector(`[data-section="${section}"]`)?.classList.add('active');
        
        this.currentSection = section;
        this.loadSection(section);
    }

    loadSection(section) {
        const contentArea = document.getElementById('contentArea');
        
        switch (section) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'employees':
                this.loadEmployees();
                break;
            case 'attendance':
                this.loadAttendance();
                break;
            case 'leaves':
                this.loadLeaves();
                break;
            case 'payroll':
                this.loadPayroll();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'permissions':
                this.loadPermissions();
                break;
            case 'advances':
                this.loadAdvances();
                break;
            case 'penalties':
                this.loadPenalties();
                break;
            case 'productivity':
                this.loadProductivity();
                break;
            case 'departments':
                this.loadDepartments();
                break;
            case 'documents':
                this.loadDocuments();
                break;
            case 'settings':
                this.loadSettings();
                break;
            case 'salary-details':
                this.loadSalaryDetails();
                break;
            case 'receipts':
                this.loadReceipts();
                break;
            case 'users':
                this.loadUsers();
                break;
            case 'backup':
                this.loadBackup();
                break;
            default:
                this.loadDashboard();
        }
    }

    loadDashboard() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </h2>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="stats-number" id="totalEmployees">0</div>
                                <div class="stats-label">إجمالي الموظفين</div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #28a745, #20c997);">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="stats-number" id="presentToday">0</div>
                                <div class="stats-label">حاضر اليوم</div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="stats-number" id="onLeave">0</div>
                                <div class="stats-label">في إجازة</div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #dc3545, #e83e8c);">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="stats-number" id="lateToday">0</div>
                                <div class="stats-label">متأخر اليوم</div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                إحصائيات الحضور الشهرية
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="attendanceChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2"></i>
                                التنبيهات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="notifications">
                                <div class="alert alert-warning alert-sm">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    5 عقود تنتهي هذا الشهر
                                </div>
                                <div class="alert alert-info alert-sm">
                                    <i class="fas fa-info-circle me-2"></i>
                                    3 طلبات إجازة في الانتظار
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-birthday-cake me-2"></i>
                                أعياد الميلاد
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="birthdays">
                                <p class="text-muted">لا توجد أعياد ميلاد هذا الأسبوع</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.updateDashboardStats();
        this.initAttendanceChart();
    }

    updateDashboardStats() {
        // Get data from database
        const employees = Database.getEmployees();
        const attendance = Database.getTodayAttendance();
        const leaves = Database.getActiveLeaves();

        // Calculate accurate stats
        const activeEmployees = employees.filter(emp => emp.status === 'active');
        const presentToday = attendance.filter(a => a.status === 'present' || a.status === 'early').length;
        const lateToday = attendance.filter(a => a.status === 'late').length;
        const onLeaveToday = leaves.length;

        // Update stats
        document.getElementById('totalEmployees').textContent = activeEmployees.length;
        document.getElementById('presentToday').textContent = presentToday;
        document.getElementById('onLeave').textContent = onLeaveToday;
        document.getElementById('lateToday').textContent = lateToday;

        // Update notifications and birthdays
        this.updateNotifications();
        this.updateBirthdays();
    }

    updateNotifications() {
        const notificationsContainer = document.getElementById('notifications');
        const notifications = this.calculateNotifications();

        if (notifications.length === 0) {
            notificationsContainer.innerHTML = `
                <div class="alert alert-success alert-sm">
                    <i class="fas fa-check-circle me-2"></i>
                    لا توجد تنبيهات جديدة
                </div>
            `;
            return;
        }

        notificationsContainer.innerHTML = notifications.map(notification => `
            <div class="alert alert-${notification.type} alert-sm">
                <i class="fas fa-${notification.icon} me-2"></i>
                ${notification.message}
            </div>
        `).join('');
    }

    calculateNotifications() {
        const notifications = [];
        const today = new Date();
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());

        // Check contracts expiring soon
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const expiringContracts = employees.filter(emp => {
            if (!emp.contract_end_date) return false;
            const contractEnd = new Date(emp.contract_end_date);
            return contractEnd <= nextMonth && contractEnd >= today;
        });

        if (expiringContracts.length > 0) {
            notifications.push({
                type: 'warning',
                icon: 'exclamation-triangle',
                message: `${expiringContracts.length} عقد${expiringContracts.length > 1 ? ' تنتهي' : ' ينتهي'} خلال الشهر القادم`
            });
        }

        // Check pending leave requests
        const pendingLeaves = Database.getAll('leaves').filter(leave => leave.status === 'pending');
        if (pendingLeaves.length > 0) {
            notifications.push({
                type: 'info',
                icon: 'info-circle',
                message: `${pendingLeaves.length} طلب${pendingLeaves.length > 1 ? ' إجازة' : ' إجازة'} في الانتظار`
            });
        }

        // Check pending permission requests
        const pendingPermissions = Database.getAll('permissions').filter(perm => perm.status === 'pending');
        if (pendingPermissions.length > 0) {
            notifications.push({
                type: 'info',
                icon: 'clock',
                message: `${pendingPermissions.length} طلب${pendingPermissions.length > 1 ? ' إذن' : ' إذن'} في الانتظار`
            });
        }

        // Check employees with high absence rate
        const highAbsenceEmployees = this.getHighAbsenceEmployees();
        if (highAbsenceEmployees.length > 0) {
            notifications.push({
                type: 'warning',
                icon: 'user-times',
                message: `${highAbsenceEmployees.length} موظف${highAbsenceEmployees.length > 1 ? 'ين' : ''} بمعدل غياب عالي`
            });
        }

        // Check unpaid advances
        const unpaidAdvances = Database.getAll('advances').filter(adv => adv.status === 'approved' && adv.remaining_amount > 0);
        if (unpaidAdvances.length > 0) {
            notifications.push({
                type: 'warning',
                icon: 'money-bill-wave',
                message: `${unpaidAdvances.length} سلفة${unpaidAdvances.length > 1 ? ' غير مسددة' : ' غير مسددة'}`
            });
        }

        // Check documents pending approval
        const pendingDocuments = Database.getAll('documents').filter(doc => doc.status === 'pending');
        if (pendingDocuments.length > 0) {
            notifications.push({
                type: 'info',
                icon: 'file-alt',
                message: `${pendingDocuments.length} وثيقة${pendingDocuments.length > 1 ? ' تحتاج' : ' تحتاج'} للموافقة`
            });
        }

        return notifications;
    }

    getHighAbsenceEmployees() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const currentMonth = new Date().toISOString().slice(0, 7);
        const attendance = Database.getAttendance({ month: currentMonth });

        return employees.filter(emp => {
            const empAttendance = attendance.filter(att => att.employee_id === emp.id);
            const absentDays = empAttendance.filter(att => att.status === 'absent').length;
            const totalWorkDays = this.getWorkDaysInMonth();
            const absenceRate = (absentDays / totalWorkDays) * 100;
            return absenceRate > 20; // More than 20% absence rate
        });
    }

    getWorkDaysInMonth() {
        const today = new Date();
        const year = today.getFullYear();
        const month = today.getMonth();
        const daysInMonth = new Date(year, month + 1, 0).getDate();

        let workDays = 0;
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(year, month, day);
            const dayOfWeek = date.getDay();
            // Assuming Sunday to Thursday are work days (0 = Sunday, 4 = Thursday)
            if (dayOfWeek >= 0 && dayOfWeek <= 4) {
                workDays++;
            }
        }
        return workDays;
    }

    updateBirthdays() {
        const birthdaysContainer = document.getElementById('birthdays');
        const birthdays = this.getUpcomingBirthdays();

        if (birthdays.length === 0) {
            birthdaysContainer.innerHTML = `
                <p class="text-muted mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    لا توجد أعياد ميلاد هذا الأسبوع
                </p>
            `;
            return;
        }

        birthdaysContainer.innerHTML = birthdays.map(birthday => `
            <div class="d-flex align-items-center mb-2 p-2 bg-light rounded">
                <div class="me-3">
                    <i class="fas fa-birthday-cake text-warning"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-bold">${birthday.name}</div>
                    <small class="text-muted">${birthday.date}</small>
                </div>
                <div>
                    <span class="badge bg-primary">${birthday.daysLeft} ${birthday.daysLeft === 0 ? 'اليوم' : 'يوم'}</span>
                </div>
            </div>
        `).join('');
    }

    getUpcomingBirthdays() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active' && emp.birth_date);
        const today = new Date();
        const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

        return employees.map(emp => {
            const birthDate = new Date(emp.birth_date);
            const thisYearBirthday = new Date(today.getFullYear(), birthDate.getMonth(), birthDate.getDate());

            // If birthday already passed this year, check next year
            if (thisYearBirthday < today) {
                thisYearBirthday.setFullYear(today.getFullYear() + 1);
            }

            const daysLeft = Math.ceil((thisYearBirthday - today) / (1000 * 60 * 60 * 24));

            return {
                name: emp.name,
                date: thisYearBirthday.toLocaleDateString('ar-SA'),
                daysLeft: daysLeft,
                birthday: thisYearBirthday
            };
        })
        .filter(birthday => birthday.birthday <= nextWeek)
        .sort((a, b) => a.daysLeft - b.daysLeft);
    }

    initAttendanceChart() {
        const ctx = document.getElementById('attendanceChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'معدل الحضور',
                        data: [85, 90, 88, 92, 87, 95],
                        borderColor: '#0d6efd',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
    }

    loadEmployees() {
        if (typeof EmployeeManager !== 'undefined') {
            new EmployeeManager().render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل قسم الموظفين...</h3></div>';
        }
    }

    loadAttendance() {
        // تنظيف المدير السابق إن وجد
        if (this.currentManager && typeof this.currentManager.cleanup === 'function') {
            this.currentManager.cleanup();
        }

        if (typeof AttendanceManager !== 'undefined') {
            this.currentManager = new AttendanceManager();
            this.currentManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل قسم الدوام...</h3></div>';
        }
    }

    loadLeaves() {
        if (typeof LeaveManager !== 'undefined') {
            window.leaveManager = new LeaveManager();
            window.leaveManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل قسم الإجازات...</h3></div>';
        }
    }

    loadPayroll() {
        // تنظيف المدير السابق إن وجد
        if (this.currentManager && typeof this.currentManager.cleanup === 'function') {
            this.currentManager.cleanup();
        }

        if (typeof PayrollManager !== 'undefined') {
            this.currentManager = new PayrollManager();
            this.currentManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل قسم الرواتب...</h3></div>';
        }
    }

    loadReports() {
        if (typeof ReportsManager !== 'undefined') {
            new ReportsManager().render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل التقارير...</h3></div>';
        }
    }

    loadPermissions() {
        if (typeof PermissionsManager !== 'undefined') {
            window.permissionsManager = new PermissionsManager();
            window.permissionsManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل الأذونات...</h3></div>';
        }
    }

    loadAdvances() {
        if (typeof AdvancesManager !== 'undefined') {
            window.advancesManager = new AdvancesManager();
            window.advancesManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل السلف...</h3></div>';
        }
    }

    loadPenalties() {
        if (typeof PenaltiesManager !== 'undefined') {
            window.penaltiesManager = new PenaltiesManager();
            window.penaltiesManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل الجزاءات والعقوبات...</h3></div>';
        }
    }

    loadProductivity() {
        if (typeof ProductivityManager !== 'undefined') {
            window.productivityManager = new ProductivityManager();
            window.productivityManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل إنتاجية الموظفين...</h3></div>';
        }
    }

    loadDepartments() {
        if (typeof DepartmentsManager !== 'undefined') {
            window.departmentsManager = new DepartmentsManager();
            window.departmentsManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل الأقسام...</h3></div>';
        }
    }

    loadDocuments() {
        if (typeof DocumentsManager !== 'undefined') {
            window.documentsManager = new DocumentsManager();
            window.documentsManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل الوثائق...</h3></div>';
        }
    }

    loadSettings() {
        if (typeof SettingsManager !== 'undefined') {
            window.settingsManager = new SettingsManager();
            window.settingsManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل الإعدادات...</h3></div>';
        }
    }

    loadSalaryDetails() {
        if (typeof SalaryDetailsManager !== 'undefined') {
            window.salaryDetailsManager = new SalaryDetailsManager();
            window.salaryDetailsManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل بيانات الراتب...</h3></div>';
        }
    }

    loadReceipts() {
        if (typeof ReceiptsManager !== 'undefined') {
            window.receiptsManager = new ReceiptsManager();
            window.receiptsManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل الإيصالات...</h3></div>';
        }
    }

    loadUsers() {
        if (typeof UserManager !== 'undefined') {
            window.userManager = new UserManager();
            window.userManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل إدارة المستخدمين...</h3></div>';
        }
    }

    loadBackup() {
        if (typeof BackupManager !== 'undefined') {
            window.backupManager = new BackupManager();
            window.backupManager.render();
        } else {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="text-center"><h3>جاري تحميل النسخ الاحتياطي...</h3></div>';
        }
    }

    logout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            localStorage.removeItem('sam_current_user');
            this.currentUser = null;
            this.showLoginScreen();
        }
    }

    handleResize() {
        // Handle responsive behavior
        if (window.innerWidth < 768) {
            // Mobile view adjustments
            document.querySelectorAll('.stats-card').forEach(card => {
                card.style.textAlign = 'center';
            });
        }
    }

    // Utility methods
    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.insertBefore(alertDiv, document.body.firstChild);
        
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-SA');
    }

    formatDateTime(date) {
        return new Date(date).toLocaleString('ar-SA');
    }

    formatCurrency(amount) {
        const settings = Database.getSettings();
        const currency = settings.company?.currency || 'SAR';
        const symbol = settings.company?.currency_symbol || 'ر.س';

        if (symbol) {
            return `${parseFloat(amount || 0).toLocaleString('ar-SA')} ${symbol}`;
        } else {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: currency
            }).format(amount || 0);
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.samApp = new SAMApp();
});
