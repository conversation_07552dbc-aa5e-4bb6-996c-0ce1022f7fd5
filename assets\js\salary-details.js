/**
 * SAM - نظام إدارة شؤون الموظفين
 * Detailed Salary Report Module
 * وحدة تقرير بيانات الراتب المفصل
 */

class SalaryDetailsManager {
    constructor() {
        this.currentMonth = new Date().toISOString().slice(0, 7);
        this.selectedEmployee = '';
    }

    render() {
        if (!window.authManager.hasPermission('payroll')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadSalaryData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-file-invoice-dollar me-2"></i>
                            بيانات الراتب المفصل
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-primary" id="generateDetailedBtn">
                                <i class="fas fa-calculator me-2"></i>
                                إنشاء تقرير مفصل
                            </button>
                            <button class="btn btn-success" id="generateSummaryBtn">
                                <i class="fas fa-chart-bar me-2"></i>
                                إنشاء تقرير إجمالي
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <label class="form-label">الشهر</label>
                    <input type="month" class="form-control" id="monthFilter" value="${this.currentMonth}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">الموظف</label>
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary flex-fill" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-outline-info" id="printDetailedBtn">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalEmployees">0</h4>
                                    <p class="mb-0">عدد الموظفين</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalBasicSalary">0</h4>
                                    <p class="mb-0">إجمالي الرواتب الأساسية</p>
                                </div>
                                <i class="fas fa-money-bill fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalAllowances">0</h4>
                                    <p class="mb-0">إجمالي البدلات</p>
                                </div>
                                <i class="fas fa-plus-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalDeductions">0</h4>
                                    <p class="mb-0">إجمالي الخصومات</p>
                                </div>
                                <i class="fas fa-minus-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Report -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        تفاصيل الرواتب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th rowspan="2">الموظف</th>
                                    <th colspan="4" class="text-center">الاستحقاقات</th>
                                    <th colspan="6" class="text-center">الخصومات</th>
                                    <th rowspan="2">صافي الراتب</th>
                                </tr>
                                <tr>
                                    <th>الراتب الأساسي</th>
                                    <th>البدلات</th>
                                    <th>الإضافي</th>
                                    <th>الإجمالي</th>
                                    <th>التأمينات</th>
                                    <th>الضرائب</th>
                                    <th>السلف</th>
                                    <th>التأخير</th>
                                    <th>الجزاءات</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody id="salaryDetailsBody">
                                <!-- Salary details will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Summary Report Modal -->
            <div class="modal fade" id="summaryModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">التقرير الإجمالي للرواتب</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="summaryContent">
                            <!-- Summary content will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="printSummaryBtn">طباعة</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Generate detailed report
        document.getElementById('generateDetailedBtn').addEventListener('click', () => {
            this.generateDetailedReport();
        });

        // Generate summary report
        document.getElementById('generateSummaryBtn').addEventListener('click', () => {
            this.generateSummaryReport();
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadSalaryData();
        });

        // Print detailed
        document.getElementById('printDetailedBtn').addEventListener('click', () => {
            this.printDetailedReport();
        });

        // Print summary
        document.getElementById('printSummaryBtn').addEventListener('click', () => {
            this.printSummaryReport();
        });

        // Month filter
        document.getElementById('monthFilter').addEventListener('change', (e) => {
            this.currentMonth = e.target.value;
            this.loadSalaryData();
        });

        // Employee filter
        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadSalaryData();
        });
    }

    loadSalaryData() {
        this.loadEmployeeOptions();
        this.loadSalaryDetails();
        this.updateSummaryCards();
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const select = document.getElementById('employeeFilter');
        
        select.innerHTML = '<option value="">جميع الموظفين</option>';
        employees.forEach(emp => {
            select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
        });
    }

    loadSalaryDetails() {
        const employees = Database.getEmployees();
        let salaryData = [];

        employees.forEach(employee => {
            if (this.selectedEmployee && employee.id !== this.selectedEmployee) return;
            if (employee.status !== 'active') return;

            const calculations = window.overtimeCalculator.calculateAllowancesAndDeductions(employee.id, this.currentMonth);
            
            if (calculations) {
                salaryData.push({
                    employee: employee,
                    calculations: calculations
                });
            }
        });

        this.renderSalaryDetails(salaryData);
    }

    renderSalaryDetails(salaryData) {
        const tbody = document.getElementById('salaryDetailsBody');

        if (salaryData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="12" class="text-center py-4">
                        <i class="fas fa-file-invoice-dollar fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد بيانات رواتب للفترة المحددة</p>
                    </td>
                </tr>
            `;
            return;
        }

        let totalBasicSalary = 0;
        let totalAllowances = 0;
        let totalDeductions = 0;
        let totalNetSalary = 0;

        const rows = salaryData.map(data => {
            const emp = data.employee;
            const calc = data.calculations;

            const basicSalary = emp.salary || 0;
            const allowances = calc.allowances.total;
            const deductions = calc.deductions.total;
            const netSalary = basicSalary + allowances - deductions;

            totalBasicSalary += basicSalary;
            totalAllowances += allowances;
            totalDeductions += deductions;
            totalNetSalary += netSalary;

            return `
                <tr>
                    <td>
                        <div class="fw-bold">${emp.name}</div>
                        <small class="text-muted">${emp.employee_number}</small>
                    </td>
                    <td>${window.samApp.formatCurrency(basicSalary)}</td>
                    <td>${window.samApp.formatCurrency(calc.allowances.transport + calc.allowances.housing + calc.allowances.other)}</td>
                    <td>${window.samApp.formatCurrency(calc.allowances.overtime)}</td>
                    <td class="fw-bold">${window.samApp.formatCurrency(basicSalary + allowances)}</td>
                    <td>${window.samApp.formatCurrency(calc.deductions.social_insurance || 0)}</td>
                    <td>${window.samApp.formatCurrency(calc.deductions.tax || 0)}</td>
                    <td>${window.samApp.formatCurrency(calc.deductions.advances)}</td>
                    <td>${window.samApp.formatCurrency(calc.deductions.lateness)}</td>
                    <td>${window.samApp.formatCurrency(calc.deductions.penalties)}</td>
                    <td class="fw-bold text-danger">${window.samApp.formatCurrency(deductions)}</td>
                    <td class="fw-bold text-success">${window.samApp.formatCurrency(netSalary)}</td>
                </tr>
            `;
        }).join('');

        // Add totals row
        const totalsRow = `
            <tr class="table-warning fw-bold">
                <td>الإجمالي</td>
                <td>${window.samApp.formatCurrency(totalBasicSalary)}</td>
                <td colspan="2">-</td>
                <td>${window.samApp.formatCurrency(totalBasicSalary + totalAllowances)}</td>
                <td colspan="4">-</td>
                <td>${window.samApp.formatCurrency(totalDeductions)}</td>
                <td>${window.samApp.formatCurrency(totalNetSalary)}</td>
            </tr>
        `;

        tbody.innerHTML = rows + totalsRow;
    }

    updateSummaryCards() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        let totalBasicSalary = 0;
        let totalAllowances = 0;
        let totalDeductions = 0;

        employees.forEach(employee => {
            if (this.selectedEmployee && employee.id !== this.selectedEmployee) return;

            const calculations = window.overtimeCalculator.calculateAllowancesAndDeductions(employee.id, this.currentMonth);

            if (calculations) {
                totalBasicSalary += employee.salary || 0;
                totalAllowances += calculations.allowances.total;
                totalDeductions += calculations.deductions.total;
            }
        });

        document.getElementById('totalEmployees').textContent = this.selectedEmployee ? 1 : employees.length;
        document.getElementById('totalBasicSalary').textContent = window.samApp.formatCurrency(totalBasicSalary);
        document.getElementById('totalAllowances').textContent = window.samApp.formatCurrency(totalAllowances);
        document.getElementById('totalDeductions').textContent = window.samApp.formatCurrency(totalDeductions);
    }

    generateDetailedReport() {
        this.loadSalaryData();
        window.samApp.showAlert('تم إنشاء التقرير المفصل بنجاح', 'success');
    }

    generateSummaryReport() {
        const modal = new bootstrap.Modal(document.getElementById('summaryModal'));
        const content = document.getElementById('summaryContent');

        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const departments = Database.getAll('departments') || [];

        // Group by department
        const departmentSummary = {};
        let grandTotal = {
            employees: 0,
            basicSalary: 0,
            allowances: 0,
            deductions: 0,
            netSalary: 0
        };

        employees.forEach(employee => {
            if (this.selectedEmployee && employee.id !== this.selectedEmployee) return;

            const deptName = employee.department || 'غير محدد';
            if (!departmentSummary[deptName]) {
                departmentSummary[deptName] = {
                    employees: 0,
                    basicSalary: 0,
                    allowances: 0,
                    deductions: 0,
                    netSalary: 0
                };
            }

            const calculations = window.overtimeCalculator.calculateAllowancesAndDeductions(employee.id, this.currentMonth);

            if (calculations) {
                const basicSalary = employee.salary || 0;
                const allowances = calculations.allowances.total;
                const deductions = calculations.deductions.total;
                const netSalary = basicSalary + allowances - deductions;

                departmentSummary[deptName].employees++;
                departmentSummary[deptName].basicSalary += basicSalary;
                departmentSummary[deptName].allowances += allowances;
                departmentSummary[deptName].deductions += deductions;
                departmentSummary[deptName].netSalary += netSalary;

                grandTotal.employees++;
                grandTotal.basicSalary += basicSalary;
                grandTotal.allowances += allowances;
                grandTotal.deductions += deductions;
                grandTotal.netSalary += netSalary;
            }
        });

        content.innerHTML = this.generateSummaryHTML(departmentSummary, grandTotal);
        modal.show();
    }

    generateSummaryHTML(departmentSummary, grandTotal) {
        const settings = Database.getSettings();

        return `
            <div class="text-center mb-4">
                <h3>${settings.company.name}</h3>
                <h4>التقرير الإجمالي للرواتب</h4>
                <p>شهر: ${this.formatMonth(this.currentMonth)}</p>
            </div>

            <table class="table table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th>القسم</th>
                        <th>عدد الموظفين</th>
                        <th>الرواتب الأساسية</th>
                        <th>البدلات</th>
                        <th>الخصومات</th>
                        <th>صافي الرواتب</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.keys(departmentSummary).map(dept => {
                        const data = departmentSummary[dept];
                        return `
                            <tr>
                                <td>${dept}</td>
                                <td>${data.employees}</td>
                                <td>${window.samApp.formatCurrency(data.basicSalary)}</td>
                                <td>${window.samApp.formatCurrency(data.allowances)}</td>
                                <td>${window.samApp.formatCurrency(data.deductions)}</td>
                                <td class="fw-bold">${window.samApp.formatCurrency(data.netSalary)}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
                <tfoot class="table-warning">
                    <tr class="fw-bold">
                        <td>الإجمالي العام</td>
                        <td>${grandTotal.employees}</td>
                        <td>${window.samApp.formatCurrency(grandTotal.basicSalary)}</td>
                        <td>${window.samApp.formatCurrency(grandTotal.allowances)}</td>
                        <td>${window.samApp.formatCurrency(grandTotal.deductions)}</td>
                        <td>${window.samApp.formatCurrency(grandTotal.netSalary)}</td>
                    </tr>
                </tfoot>
            </table>
        `;
    }

    printDetailedReport() {
        const tableContent = document.querySelector('#salaryDetailsBody').parentElement.outerHTML;
        const title = `تقرير بيانات الراتب المفصل - ${this.formatMonth(this.currentMonth)}`;

        window.printManager.printReport(title, tableContent, {
            showDate: true,
            showCompanyInfo: true
        });
    }

    printSummaryReport() {
        const content = document.getElementById('summaryContent').innerHTML;
        const title = `التقرير الإجمالي للرواتب - ${this.formatMonth(this.currentMonth)}`;

        window.printManager.printReport(title, content, {
            showDate: true,
            showCompanyInfo: true
        });
    }

    formatMonth(monthStr) {
        const [year, month] = monthStr.split('-');
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[parseInt(month) - 1]} ${year}`;
    }
}

// Global reference
let salaryDetailsManager;
