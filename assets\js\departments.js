/**
 * SAM - نظام إدارة شؤون الموظفين
 * Departments Management Module
 * وحدة إدارة الأقسام
 */

class DepartmentsManager {
    constructor() {
        this.selectedDepartment = null;
    }

    render() {
        if (!window.authManager.hasPermission('employees')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadDepartmentsData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-building me-2"></i>
                            إدارة الأقسام
                        </h2>
                        <button class="btn btn-primary" id="addDepartmentBtn">
                            <i class="fas fa-plus me-2"></i>
                            إضافة قسم جديد
                        </button>
                    </div>
                </div>
            </div>

            <!-- Departments Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalDepartments">0</h4>
                                    <p class="mb-0">إجمالي الأقسام</p>
                                </div>
                                <i class="fas fa-building fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="activeDepartments">0</h4>
                                    <p class="mb-0">أقسام نشطة</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalEmployees">0</h4>
                                    <p class="mb-0">إجمالي الموظفين</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="avgEmployeesPerDept">0</h4>
                                    <p class="mb-0">متوسط الموظفين/قسم</p>
                                </div>
                                <i class="fas fa-chart-bar fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Departments Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم القسم</th>
                                    <th>الكود</th>
                                    <th>المدير</th>
                                    <th>عدد الموظفين</th>
                                    <th>الميزانية</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="departmentsTableBody">
                                <!-- Departments will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Department Modal -->
            <div class="modal fade" id="departmentModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="departmentModalTitle">إضافة قسم جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="departmentForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم القسم *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">كود القسم *</label>
                                        <input type="text" class="form-control" name="code" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">مدير القسم</label>
                                        <select class="form-select" name="manager_id">
                                            <option value="">اختر مدير القسم</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">القسم الرئيسي</label>
                                        <select class="form-select" name="parent_id">
                                            <option value="">لا يوجد (قسم رئيسي)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الميزانية السنوية</label>
                                        <input type="number" class="form-control" name="budget" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحد الأقصى للموظفين</label>
                                        <input type="number" class="form-control" name="max_employees" min="1">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموقع</label>
                                        <input type="text" class="form-control" name="location">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="active">نشط</option>
                                            <option value="inactive">غير نشط</option>
                                            <option value="suspended">موقوف</option>
                                        </select>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">الوصف</label>
                                        <textarea class="form-control" name="description" rows="3"></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">المهام والمسؤوليات</label>
                                        <textarea class="form-control" name="responsibilities" rows="3"></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">أهداف القسم</label>
                                        <textarea class="form-control" name="objectives" rows="3"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="saveDepartmentBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Department Details Modal -->
            <div class="modal fade" id="departmentDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل القسم</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="departmentDetailsContent">
                            <!-- Department details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="editDepartmentBtn">تعديل</button>
                            <button type="button" class="btn btn-success" id="printDepartmentBtn">طباعة</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Add department button
        document.getElementById('addDepartmentBtn').addEventListener('click', () => {
            this.showDepartmentModal();
        });

        // Save department
        document.getElementById('saveDepartmentBtn').addEventListener('click', () => {
            this.saveDepartment();
        });

        // Edit department from details modal
        document.getElementById('editDepartmentBtn').addEventListener('click', () => {
            if (this.selectedDepartment) {
                const detailsModal = bootstrap.Modal.getInstance(document.getElementById('departmentDetailsModal'));
                detailsModal.hide();
                this.showDepartmentModal(this.selectedDepartment);
            }
        });

        // Print department
        document.getElementById('printDepartmentBtn').addEventListener('click', () => {
            if (this.selectedDepartment) {
                this.printDepartment(this.selectedDepartment);
            }
        });
    }

    loadDepartmentsData() {
        this.loadManagerOptions();
        this.loadParentDepartmentOptions();
        this.loadDepartmentsTable();
        this.updateDepartmentStats();
    }

    loadManagerOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const selects = document.querySelectorAll('select[name="manager_id"]');

        selects.forEach(select => {
            select.innerHTML = '<option value="">اختر مدير القسم</option>';
            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadParentDepartmentOptions() {
        const departments = Database.getAll('departments') || [];
        const selects = document.querySelectorAll('select[name="parent_id"]');

        selects.forEach(select => {
            select.innerHTML = '<option value="">لا يوجد (قسم رئيسي)</option>';
            departments.forEach(dept => {
                select.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
            });
        });
    }

    loadDepartmentsTable() {
        const departments = Database.getAll('departments') || [];
        this.renderDepartmentsTable(departments);
    }

    renderDepartmentsTable(departments) {
        const tbody = document.getElementById('departmentsTableBody');
        const employees = Database.getEmployees();

        if (departments.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="fas fa-building fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد أقسام</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = departments.map(dept => {
            const manager = employees.find(emp => emp.id === dept.manager_id);
            const deptEmployees = employees.filter(emp => emp.department === dept.id);
            const statusBadge = this.getStatusBadge(dept.status);

            return `
                <tr>
                    <td>
                        <div class="fw-bold">${dept.name}</div>
                        <small class="text-muted">${dept.description || ''}</small>
                    </td>
                    <td><span class="badge bg-secondary">${dept.code}</span></td>
                    <td>${manager ? manager.name : 'غير محدد'}</td>
                    <td>
                        <span class="badge bg-info">${deptEmployees.length}</span>
                        ${dept.max_employees ? `/ ${dept.max_employees}` : ''}
                    </td>
                    <td>${dept.budget ? window.samApp.formatCurrency(dept.budget) : '-'}</td>
                    <td>${statusBadge}</td>
                    <td>${window.samApp.formatDate(dept.created_at)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary view-department"
                                    data-department-id="${dept.id}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success edit-department"
                                    data-department-id="${dept.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger delete-department"
                                    data-department-id="${dept.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View department
        document.querySelectorAll('.view-department').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const deptId = e.target.closest('.view-department').dataset.departmentId;
                this.viewDepartment(deptId);
            });
        });

        // Edit department
        document.querySelectorAll('.edit-department').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const deptId = e.target.closest('.edit-department').dataset.departmentId;
                const department = Database.getById('departments', deptId);
                this.showDepartmentModal(department);
            });
        });

        // Delete department
        document.querySelectorAll('.delete-department').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const deptId = e.target.closest('.delete-department').dataset.departmentId;
                this.deleteDepartment(deptId);
            });
        });
    }

    getStatusBadge(status) {
        const statusMap = {
            'active': { class: 'success', text: 'نشط' },
            'inactive': { class: 'secondary', text: 'غير نشط' },
            'suspended': { class: 'warning', text: 'موقوف' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    updateDepartmentStats() {
        const departments = Database.getAll('departments') || [];
        const employees = Database.getEmployees();

        const totalDepartments = departments.length;
        const activeDepartments = departments.filter(d => d.status === 'active').length;
        const totalEmployees = employees.length;
        const avgEmployeesPerDept = totalDepartments > 0 ? Math.round(totalEmployees / totalDepartments) : 0;

        document.getElementById('totalDepartments').textContent = totalDepartments;
        document.getElementById('activeDepartments').textContent = activeDepartments;
        document.getElementById('totalEmployees').textContent = totalEmployees;
        document.getElementById('avgEmployeesPerDept').textContent = avgEmployeesPerDept;
    }

    showDepartmentModal(department = null) {
        const modal = new bootstrap.Modal(document.getElementById('departmentModal'));
        const form = document.getElementById('departmentForm');
        const title = document.getElementById('departmentModalTitle');

        if (department) {
            title.textContent = 'تعديل القسم';
            this.populateDepartmentForm(form, department);
            this.selectedDepartment = department;
        } else {
            title.textContent = 'إضافة قسم جديد';
            form.reset();
            form.querySelector('select[name="status"]').value = 'active';
            this.selectedDepartment = null;
        }

        modal.show();
    }

    populateDepartmentForm(form, department) {
        Object.keys(department).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = department[key] || '';
            }
        });
    }

    saveDepartment() {
        const form = document.getElementById('departmentForm');
        const formData = new FormData(form);
        const departmentData = Object.fromEntries(formData.entries());

        // Convert numeric fields
        const numericFields = ['budget', 'max_employees'];
        numericFields.forEach(field => {
            if (departmentData[field]) {
                departmentData[field] = parseFloat(departmentData[field]) || 0;
            }
        });

        try {
            if (this.selectedDepartment) {
                // Update existing department
                Database.update('departments', this.selectedDepartment.id, departmentData);
                window.samApp.showAlert('تم تحديث القسم بنجاح', 'success');
            } else {
                // Create new department
                Database.create('departments', departmentData);
                window.samApp.showAlert('تم إضافة القسم بنجاح', 'success');
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('departmentModal'));
            modal.hide();
            this.loadDepartmentsData();

        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    viewDepartment(departmentId) {
        const department = Database.getById('departments', departmentId);
        if (!department) {
            window.samApp.showAlert('القسم غير موجود', 'danger');
            return;
        }

        this.showDepartmentDetails(department);
    }

    showDepartmentDetails(department) {
        const modal = new bootstrap.Modal(document.getElementById('departmentDetailsModal'));
        const content = document.getElementById('departmentDetailsContent');

        const employees = Database.getEmployees();
        const manager = employees.find(emp => emp.id === department.manager_id);
        const deptEmployees = employees.filter(emp => emp.department === department.id);
        const parentDept = department.parent_id ? Database.getById('departments', department.parent_id) : null;

        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات أساسية</h6>
                    <p><strong>اسم القسم:</strong> ${department.name}</p>
                    <p><strong>الكود:</strong> ${department.code}</p>
                    <p><strong>المدير:</strong> ${manager ? manager.name : 'غير محدد'}</p>
                    <p><strong>القسم الرئيسي:</strong> ${parentDept ? parentDept.name : 'لا يوجد'}</p>
                    <p><strong>الحالة:</strong> ${this.getStatusBadge(department.status)}</p>
                    <p><strong>تاريخ الإنشاء:</strong> ${window.samApp.formatDate(department.created_at)}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات الاتصال والموقع</h6>
                    <p><strong>الموقع:</strong> ${department.location || 'غير محدد'}</p>
                    <p><strong>الهاتف:</strong> ${department.phone || 'غير محدد'}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${department.email || 'غير محدد'}</p>
                    <p><strong>الميزانية السنوية:</strong> ${department.budget ? window.samApp.formatCurrency(department.budget) : 'غير محددة'}</p>
                    <p><strong>الحد الأقصى للموظفين:</strong> ${department.max_employees || 'غير محدد'}</p>
                    <p><strong>عدد الموظفين الحالي:</strong> ${deptEmployees.length}</p>
                </div>
                <div class="col-12">
                    <h6 class="text-primary">الوصف</h6>
                    <p>${department.description || 'لا يوجد وصف'}</p>
                </div>
                ${department.responsibilities ? `
                <div class="col-12">
                    <h6 class="text-primary">المهام والمسؤوليات</h6>
                    <p>${department.responsibilities}</p>
                </div>
                ` : ''}
                ${department.objectives ? `
                <div class="col-12">
                    <h6 class="text-primary">أهداف القسم</h6>
                    <p>${department.objectives}</p>
                </div>
                ` : ''}
                <div class="col-12">
                    <h6 class="text-primary">موظفي القسم (${deptEmployees.length})</h6>
                    ${deptEmployees.length > 0 ? `
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الرقم الوظيفي</th>
                                        <th>المنصب</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${deptEmployees.map(emp => `
                                        <tr>
                                            <td>${emp.name}</td>
                                            <td>${emp.employee_number}</td>
                                            <td>${emp.position || 'غير محدد'}</td>
                                            <td><span class="badge bg-${emp.status === 'active' ? 'success' : 'secondary'}">${emp.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    ` : '<p class="text-muted">لا يوجد موظفين في هذا القسم</p>'}
                </div>
            </div>
        `;

        this.selectedDepartment = department;
        modal.show();
    }

    deleteDepartment(departmentId) {
        const department = Database.getById('departments', departmentId);
        const employees = Database.getEmployees();
        const deptEmployees = employees.filter(emp => emp.department === departmentId);

        if (deptEmployees.length > 0) {
            window.samApp.showAlert('لا يمكن حذف القسم لأنه يحتوي على موظفين. يرجى نقل الموظفين أولاً.', 'warning');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف القسم "${department?.name}"؟`)) {
            try {
                Database.delete('departments', departmentId);
                window.samApp.showAlert('تم حذف القسم بنجاح', 'success');
                this.loadDepartmentsData();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    printDepartment(department) {
        const employees = Database.getEmployees();
        const manager = employees.find(emp => emp.id === department.manager_id);
        const deptEmployees = employees.filter(emp => emp.department === department.id);
        const settings = Database.getSettings();

        const printContent = `
            <div style="direction: rtl; font-family: Arial, sans-serif; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1>${settings.company.name}</h1>
                    <h2>تقرير تفاصيل القسم</h2>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <h3>معلومات القسم</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; width: 30%;"><strong>اسم القسم:</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${department.name}</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>الكود:</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${department.code}</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>المدير:</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${manager ? manager.name : 'غير محدد'}</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>الموقع:</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${department.location || 'غير محدد'}</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>الهاتف:</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${department.phone || 'غير محدد'}</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>البريد الإلكتروني:</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${department.email || 'غير محدد'}</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>الميزانية السنوية:</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${department.budget ? window.samApp.formatCurrency(department.budget) : 'غير محددة'}</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>عدد الموظفين:</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${deptEmployees.length}</td>
                        </tr>
                    </table>
                </div>

                ${department.description ? `
                <div style="margin-bottom: 20px;">
                    <h3>الوصف</h3>
                    <p style="border: 1px solid #ddd; padding: 10px; background-color: #f9f9f9;">${department.description}</p>
                </div>
                ` : ''}

                ${deptEmployees.length > 0 ? `
                <div style="margin-bottom: 20px;">
                    <h3>قائمة الموظفين</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: #f5f5f5;">
                                <th style="border: 1px solid #ddd; padding: 8px;">الاسم</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">الرقم الوظيفي</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">المنصب</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">الراتب</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">تاريخ التوظيف</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${deptEmployees.map(emp => `
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 8px;">${emp.name}</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">${emp.employee_number}</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">${emp.position || 'غير محدد'}</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">${window.samApp.formatCurrency(emp.salary || 0)}</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">${window.samApp.formatDate(emp.hire_date)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                ` : ''}

                <div style="margin-top: 40px; text-align: center; font-size: 12px; color: #666;">
                    <p>تم إنشاء هذا التقرير بواسطة نظام SAM لإدارة شؤون الموظفين</p>
                </div>
            </div>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>تقرير القسم - ${department.name}</title>
                <meta charset="utf-8">
                <style>
                    @media print {
                        body { margin: 0; }
                        @page { margin: 1cm; }
                    }
                </style>
            </head>
            <body>
                ${printContent}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// Global reference
let departmentsManager;
