/**
 * SAM - نظام إدارة شؤون الموظفين
 * User Management Module
 * وحدة إدارة المستخدمين
 */

class UserManager {
    constructor() {
        this.selectedUser = null;
    }

    render() {
        if (!window.authManager.hasPermission('users')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadUsersData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة المستخدمين
                        </h2>
                        <button class="btn btn-primary" id="addUserBtn">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مستخدم جديد
                        </button>
                    </div>
                </div>
            </div>

            <!-- Users Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalUsers">0</h4>
                                    <p class="mb-0">إجمالي المستخدمين</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="activeUsers">0</h4>
                                    <p class="mb-0">نشط</p>
                                </div>
                                <i class="fas fa-user-check fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="adminUsers">0</h4>
                                    <p class="mb-0">مديرين</p>
                                </div>
                                <i class="fas fa-user-shield fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="onlineUsers">1</h4>
                                    <p class="mb-0">متصل الآن</p>
                                </div>
                                <i class="fas fa-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم الكامل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>آخر دخول</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <!-- Users will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- User Modal -->
            <div class="modal fade" id="userModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="userModalTitle">إضافة مستخدم جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="userForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم المستخدم *</label>
                                        <input type="text" class="form-control" name="username" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">كلمة المرور *</label>
                                        <input type="password" class="form-control" name="password" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الدور *</label>
                                        <select class="form-select" name="role" required>
                                            <option value="">اختر الدور</option>
                                            <option value="admin">مدير النظام</option>
                                            <option value="hr_manager">مدير الموارد البشرية</option>
                                            <option value="accountant">محاسب</option>
                                            <option value="employee">موظف</option>
                                        </select>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">الصلاحيات</label>
                                        <div class="row" id="permissionsContainer">
                                            <!-- Permissions checkboxes will be loaded here -->
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="active">
                                            <option value="true">نشط</option>
                                            <option value="false">غير نشط</option>
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="saveUserBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Details Modal -->
            <div class="modal fade" id="userDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل المستخدم</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="userDetailsContent">
                            <!-- User details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="editUserBtn">تعديل</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Add user button
        document.getElementById('addUserBtn').addEventListener('click', () => {
            this.showUserModal();
        });

        // Save user
        document.getElementById('saveUserBtn').addEventListener('click', () => {
            this.saveUser();
        });

        // Role change event to update permissions
        document.querySelector('select[name="role"]').addEventListener('change', (e) => {
            this.updatePermissionsByRole(e.target.value);
        });

        // Edit user from details modal
        document.getElementById('editUserBtn').addEventListener('click', () => {
            if (this.selectedUser) {
                const detailsModal = bootstrap.Modal.getInstance(document.getElementById('userDetailsModal'));
                detailsModal.hide();
                this.showUserModal(this.selectedUser);
            }
        });
    }

    loadUsersData() {
        this.loadUsersTable();
        this.updateUserStats();
        this.loadPermissions();
    }

    loadUsersTable() {
        const users = window.authManager.getUsers();
        this.renderUsersTable(users);
    }

    renderUsersTable(users) {
        const tbody = document.getElementById('usersTableBody');
        
        if (users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="fas fa-users-cog fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد مستخدمين</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = users.map(user => {
            const statusBadge = this.getStatusBadge(user.active);
            const roleBadge = this.getRoleBadge(user.role);
            
            return `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                ${user.name.charAt(0).toUpperCase()}
                            </div>
                            <strong>${user.username}</strong>
                        </div>
                    </td>
                    <td>${user.name}</td>
                    <td>${user.email || '-'}</td>
                    <td>${roleBadge}</td>
                    <td>${user.last_login ? window.samApp.formatDate(user.last_login) : 'لم يسجل دخول'}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary view-user" 
                                    data-user-id="${user.id}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success edit-user" 
                                    data-user-id="${user.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-warning toggle-user" 
                                    data-user-id="${user.id}">
                                <i class="fas fa-${user.active ? 'ban' : 'check'}"></i>
                            </button>
                            ${user.id !== 'admin' ? `
                            <button class="btn btn-outline-danger delete-user" 
                                    data-user-id="${user.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View user
        document.querySelectorAll('.view-user').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.target.closest('.view-user').dataset.userId;
                this.viewUser(userId);
            });
        });

        // Edit user
        document.querySelectorAll('.edit-user').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.target.closest('.edit-user').dataset.userId;
                const users = window.authManager.getUsers();
                const user = users.find(u => u.id === userId);
                this.showUserModal(user);
            });
        });

        // Toggle user status
        document.querySelectorAll('.toggle-user').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.target.closest('.toggle-user').dataset.userId;
                this.toggleUserStatus(userId);
            });
        });

        // Delete user
        document.querySelectorAll('.delete-user').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.target.closest('.delete-user').dataset.userId;
                this.deleteUser(userId);
            });
        });
    }

    getStatusBadge(active) {
        return active ?
            '<span class="badge bg-success">نشط</span>' :
            '<span class="badge bg-secondary">غير نشط</span>';
    }

    getRoleBadge(role) {
        const roleMap = {
            'admin': { class: 'danger', text: 'مدير النظام' },
            'hr_manager': { class: 'primary', text: 'مدير الموارد البشرية' },
            'accountant': { class: 'info', text: 'محاسب' },
            'employee': { class: 'secondary', text: 'موظف' }
        };

        const roleInfo = roleMap[role] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${roleInfo.class}">${roleInfo.text}</span>`;
    }

    updateUserStats() {
        const users = window.authManager.getUsers();

        const totalUsers = users.length;
        const activeUsers = users.filter(u => u.active).length;
        const adminUsers = users.filter(u => u.role === 'admin').length;

        document.getElementById('totalUsers').textContent = totalUsers;
        document.getElementById('activeUsers').textContent = activeUsers;
        document.getElementById('adminUsers').textContent = adminUsers;
    }

    loadPermissions() {
        const permissions = window.authManager.getPermissions();
        const container = document.getElementById('permissionsContainer');

        container.innerHTML = permissions.map(perm => `
            <div class="col-md-6 mb-2">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="permissions"
                           value="${perm.id}" id="perm_${perm.id}">
                    <label class="form-check-label" for="perm_${perm.id}">
                        ${perm.name}
                    </label>
                </div>
            </div>
        `).join('');
    }

    updatePermissionsByRole(role) {
        const roles = window.authManager.getRoles();
        const selectedRole = roles.find(r => r.id === role);

        if (selectedRole) {
            // Uncheck all permissions first
            document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Check permissions for selected role
            selectedRole.permissions.forEach(permId => {
                const checkbox = document.querySelector(`input[value="${permId}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
        }
    }

    showUserModal(user = null) {
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        const form = document.getElementById('userForm');
        const title = document.getElementById('userModalTitle');

        if (user) {
            title.textContent = 'تعديل المستخدم';
            this.populateUserForm(form, user);
            this.selectedUser = user;
            // Make password field optional for editing
            form.querySelector('input[name="password"]').required = false;
            form.querySelector('input[name="password"]').placeholder = 'اتركه فارغاً للاحتفاظ بكلمة المرور الحالية';
        } else {
            title.textContent = 'إضافة مستخدم جديد';
            form.reset();
            this.selectedUser = null;
            form.querySelector('input[name="password"]').required = true;
            form.querySelector('input[name="password"]').placeholder = '';
        }

        modal.show();
    }

    populateUserForm(form, user) {
        // Populate basic fields
        Object.keys(user).forEach(key => {
            if (key !== 'permissions' && key !== 'password') {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    if (input.type === 'select-one') {
                        input.value = user[key];
                    } else {
                        input.value = user[key] || '';
                    }
                }
            }
        });

        // Handle permissions checkboxes
        document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
            checkbox.checked = user.permissions && user.permissions.includes(checkbox.value);
        });

        // Update permissions based on role
        this.updatePermissionsByRole(user.role);
    }

    saveUser() {
        const form = document.getElementById('userForm');
        const formData = new FormData(form);
        const userData = {};

        // Convert FormData to object (excluding permissions)
        for (let [key, value] of formData.entries()) {
            if (key !== 'permissions') {
                userData[key] = value;
            }
        }

        // Handle permissions checkboxes
        const selectedPermissions = Array.from(form.querySelectorAll('input[name="permissions"]:checked'))
            .map(checkbox => checkbox.value);
        userData.permissions = selectedPermissions;

        // Convert active to boolean
        userData.active = userData.active === 'true';

        try {
            if (this.selectedUser) {
                // Update existing user
                if (!userData.password) {
                    delete userData.password; // Don't update password if empty
                }
                window.authManager.updateUserData(this.selectedUser.id, userData);
                window.samApp.showAlert('تم تحديث المستخدم بنجاح', 'success');
            } else {
                // Create new user
                window.authManager.createUser(userData);
                window.samApp.showAlert('تم إضافة المستخدم بنجاح', 'success');
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();
            this.loadUsersData();

        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    viewUser(userId) {
        const users = window.authManager.getUsers();
        const user = users.find(u => u.id === userId);

        if (!user) {
            window.samApp.showAlert('المستخدم غير موجود', 'danger');
            return;
        }

        this.showUserDetails(user);
    }

    showUserDetails(user) {
        const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
        const content = document.getElementById('userDetailsContent');

        const permissions = window.authManager.getPermissions();
        const userPermissions = user.permissions || [];

        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات أساسية</h6>
                    <p><strong>اسم المستخدم:</strong> ${user.username}</p>
                    <p><strong>الاسم الكامل:</strong> ${user.name}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${user.email || 'غير محدد'}</p>
                    <p><strong>رقم الهاتف:</strong> ${user.phone || 'غير محدد'}</p>
                    <p><strong>الدور:</strong> ${this.getRoleBadge(user.role)}</p>
                    <p><strong>الحالة:</strong> ${this.getStatusBadge(user.active)}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات النشاط</h6>
                    <p><strong>تاريخ الإنشاء:</strong> ${window.samApp.formatDate(user.created_at)}</p>
                    <p><strong>آخر دخول:</strong> ${user.last_login ? window.samApp.formatDate(user.last_login) : 'لم يسجل دخول'}</p>
                </div>
                <div class="col-12">
                    <h6 class="text-primary">الصلاحيات</h6>
                    <div class="row">
                        ${userPermissions.includes('all') ?
                            '<div class="col-12"><span class="badge bg-danger">جميع الصلاحيات</span></div>' :
                            userPermissions.map(permId => {
                                const perm = permissions.find(p => p.id === permId);
                                return perm ? `<div class="col-md-6 mb-1"><span class="badge bg-primary">${perm.name}</span></div>` : '';
                            }).join('')
                        }
                    </div>
                </div>
            </div>
        `;

        this.selectedUser = user;
        modal.show();
    }

    toggleUserStatus(userId) {
        if (userId === 'admin') {
            window.samApp.showAlert('لا يمكن تعطيل حساب المدير الرئيسي', 'warning');
            return;
        }

        try {
            const user = window.authManager.toggleUserStatus(userId);
            const action = user.active ? 'تفعيل' : 'تعطيل';
            window.samApp.showAlert(`تم ${action} المستخدم بنجاح`, 'success');
            this.loadUsersData();
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    deleteUser(userId) {
        if (userId === 'admin') {
            window.samApp.showAlert('لا يمكن حذف حساب المدير الرئيسي', 'warning');
            return;
        }

        const users = window.authManager.getUsers();
        const user = users.find(u => u.id === userId);

        if (confirm(`هل أنت متأكد من حذف المستخدم "${user?.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            try {
                window.authManager.deleteUser(userId);
                window.samApp.showAlert('تم حذف المستخدم بنجاح', 'success');
                this.loadUsersData();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }
}

// Global reference
let userManager;
