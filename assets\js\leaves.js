/**
 * SAM - نظام إدارة شؤون الموظفين
 * Leave Management Module
 * وحدة إدارة الإجازات
 */

class LeaveManager {
    constructor() {
        this.currentView = 'all';
        this.selectedEmployee = '';
        this.selectedStatus = '';
    }

    render() {
        if (!window.authManager.hasPermission('leaves')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadLeaveData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-calendar-alt me-2"></i>
                            إدارة الإجازات
                        </h2>
                        <button class="btn btn-primary" id="addLeaveBtn">
                            <i class="fas fa-plus me-2"></i>
                            طلب إجازة جديد
                        </button>
                    </div>
                </div>
            </div>

            <!-- Leave Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="pendingCount">0</h4>
                                    <p class="mb-0">في الانتظار</p>
                                </div>
                                <i class="fas fa-hourglass-half fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="approvedCount">0</h4>
                                    <p class="mb-0">موافق عليها</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="rejectedCount">0</h4>
                                    <p class="mb-0">مرفوضة</p>
                                </div>
                                <i class="fas fa-times-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="activeCount">0</h4>
                                    <p class="mb-0">نشطة حالياً</p>
                                </div>
                                <i class="fas fa-calendar-check fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="pending">في الانتظار</option>
                        <option value="approved">موافق عليها</option>
                        <option value="rejected">مرفوضة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="typeFilter">
                        <option value="">جميع الأنواع</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-success" id="exportLeavesBtn">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-info" id="printLeavesBtn">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Leaves Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        طلبات الإجازات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="leavesTableBody">
                                <!-- Leave records will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Leave Request Modal -->
            <div class="modal fade" id="leaveModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="leaveModalTitle">طلب إجازة جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="leaveForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع الإجازة *</label>
                                        <select class="form-select" name="leave_type" required>
                                            <option value="">اختر نوع الإجازة</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ البداية *</label>
                                        <input type="date" class="form-control" name="start_date" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ النهاية *</label>
                                        <input type="date" class="form-control" name="end_date" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">عدد الأيام</label>
                                        <input type="number" class="form-control" name="days" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="pending">في الانتظار</option>
                                            <option value="approved">موافق عليها</option>
                                            <option value="rejected">مرفوضة</option>
                                        </select>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">سبب الإجازة *</label>
                                        <textarea class="form-control" name="reason" rows="3" required></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات الإدارة</label>
                                        <textarea class="form-control" name="admin_notes" rows="2"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="saveLeaveBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Leave Details Modal -->
            <div class="modal fade" id="leaveDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل طلب الإجازة</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="leaveDetailsContent">
                            <!-- Leave details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <div id="leaveActions">
                                <!-- Action buttons will be added here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Add leave button
        document.getElementById('addLeaveBtn').addEventListener('click', () => {
            this.showLeaveModal();
        });

        // Filter events
        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadLeaveData();
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.selectedStatus = e.target.value;
            this.loadLeaveData();
        });

        // Export button
        document.getElementById('exportLeavesBtn').addEventListener('click', () => {
            this.exportLeaves();
        });

        // Print button
        document.getElementById('printLeavesBtn').addEventListener('click', () => {
            this.printLeaves();
        });

        // Save leave
        document.getElementById('saveLeaveBtn').addEventListener('click', () => {
            this.saveLeave();
        });

        // Date change events for calculating days
        const startDateInput = document.querySelector('input[name="start_date"]');
        const endDateInput = document.querySelector('input[name="end_date"]');
        
        if (startDateInput && endDateInput) {
            [startDateInput, endDateInput].forEach(input => {
                input.addEventListener('change', () => {
                    this.calculateLeaveDays();
                });
            });
        }
    }

    loadLeaveData() {
        this.loadEmployeeOptions();
        this.loadLeaveTypeOptions();
        this.loadLeavesTable();
        this.updateLeaveStats();
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const selects = document.querySelectorAll('#employeeFilter, select[name="employee_id"]');
        
        selects.forEach(select => {
            const isFilter = select.id === 'employeeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الموظفين</option>' : '<option value="">اختر الموظف</option>';
            
            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadLeaveTypeOptions() {
        const leaveTypes = Database.getAll('leave_types');
        const selects = document.querySelectorAll('#typeFilter, select[name="leave_type"]');
        
        selects.forEach(select => {
            const isFilter = select.id === 'typeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الأنواع</option>' : '<option value="">اختر نوع الإجازة</option>';
            
            leaveTypes.forEach(type => {
                select.innerHTML += `<option value="${type.id}">${type.name}</option>`;
            });
        });
    }

    loadLeavesTable() {
        let leaves = Database.getLeaves();
        
        // Apply filters
        if (this.selectedEmployee) {
            leaves = leaves.filter(leave => leave.employee_id === this.selectedEmployee);
        }
        
        if (this.selectedStatus) {
            leaves = leaves.filter(leave => leave.status === this.selectedStatus);
        }
        
        // Sort by creation date (newest first)
        leaves.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        
        this.renderLeavesTable(leaves);
    }

    renderLeavesTable(leaves) {
        const tbody = document.getElementById('leavesTableBody');
        const employees = Database.getEmployees();
        const leaveTypes = Database.getAll('leave_types');
        
        if (leaves.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="fas fa-calendar-alt fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد طلبات إجازة</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = leaves.map(leave => {
            const employee = employees.find(emp => emp.id === leave.employee_id);
            const leaveType = leaveTypes.find(type => type.id === leave.leave_type);
            const statusBadge = this.getStatusBadge(leave.status);
            const days = this.calculateDaysBetween(leave.start_date, leave.end_date);
            
            return `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${employee?.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}" 
                                 alt="${employee?.name}" class="rounded-circle me-2" width="40" height="40">
                            <div>
                                <div class="fw-bold">${employee?.name || 'غير معروف'}</div>
                                <small class="text-muted">${employee?.employee_number || ''}</small>
                            </div>
                        </div>
                    </td>
                    <td>${leaveType?.name || 'غير محدد'}</td>
                    <td>${window.samApp.formatDate(leave.start_date)}</td>
                    <td>${window.samApp.formatDate(leave.end_date)}</td>
                    <td><span class="badge bg-info">${days} يوم</span></td>
                    <td>${statusBadge}</td>
                    <td>${window.samApp.formatDate(leave.created_at)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary view-leave" 
                                    data-leave-id="${leave.id}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success edit-leave" 
                                    data-leave-id="${leave.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger delete-leave" 
                                    data-leave-id="${leave.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View leave
        document.querySelectorAll('.view-leave').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const leaveId = e.target.closest('.view-leave').dataset.leaveId;
                this.viewLeave(leaveId);
            });
        });

        // Edit leave
        document.querySelectorAll('.edit-leave').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const leaveId = e.target.closest('.edit-leave').dataset.leaveId;
                const leave = Database.getById('leaves', leaveId);
                this.showLeaveModal(leave);
            });
        });

        // Delete leave
        document.querySelectorAll('.delete-leave').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const leaveId = e.target.closest('.delete-leave').dataset.leaveId;
                this.deleteLeave(leaveId);
            });
        });
    }

    getStatusBadge(status) {
        const statusMap = {
            'pending': { class: 'warning', text: 'في الانتظار' },
            'approved': { class: 'success', text: 'موافق عليها' },
            'rejected': { class: 'danger', text: 'مرفوضة' }
        };
        
        const statusInfo = statusMap[status] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    updateLeaveStats() {
        const leaves = Database.getLeaves();
        
        const pendingCount = leaves.filter(l => l.status === 'pending').length;
        const approvedCount = leaves.filter(l => l.status === 'approved').length;
        const rejectedCount = leaves.filter(l => l.status === 'rejected').length;
        const activeCount = Database.getActiveLeaves().length;
        
        document.getElementById('pendingCount').textContent = pendingCount;
        document.getElementById('approvedCount').textContent = approvedCount;
        document.getElementById('rejectedCount').textContent = rejectedCount;
        document.getElementById('activeCount').textContent = activeCount;
    }

    showLeaveModal(leave = null) {
        const modal = new bootstrap.Modal(document.getElementById('leaveModal'));
        const form = document.getElementById('leaveForm');
        const title = document.getElementById('leaveModalTitle');
        
        if (leave) {
            title.textContent = 'تعديل طلب الإجازة';
            this.populateLeaveForm(form, leave);
            this.selectedLeave = leave;
        } else {
            title.textContent = 'طلب إجازة جديد';
            form.reset();
            form.querySelector('select[name="status"]').value = 'pending';
            this.selectedLeave = null;
        }
        
        modal.show();
    }

    populateLeaveForm(form, leave) {
        Object.keys(leave).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = leave[key] || '';
            }
        });
        
        this.calculateLeaveDays();
    }

    calculateLeaveDays() {
        const startDate = document.querySelector('input[name="start_date"]').value;
        const endDate = document.querySelector('input[name="end_date"]').value;
        
        if (startDate && endDate) {
            const days = this.calculateDaysBetween(startDate, endDate);
            document.querySelector('input[name="days"]').value = days;
        }
    }

    calculateDaysBetween(startDate, endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates
        return diffDays;
    }

    saveLeave() {
        const form = document.getElementById('leaveForm');
        const formData = new FormData(form);
        const leaveData = Object.fromEntries(formData.entries());
        
        // Calculate days
        leaveData.days = this.calculateDaysBetween(leaveData.start_date, leaveData.end_date);
        
        try {
            if (this.selectedLeave) {
                // Update existing leave
                Database.update('leaves', this.selectedLeave.id, leaveData);
                window.samApp.showAlert('تم تحديث طلب الإجازة بنجاح', 'success');
            } else {
                // Create new leave
                Database.create('leaves', leaveData);
                window.samApp.showAlert('تم إضافة طلب الإجازة بنجاح', 'success');
            }
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('leaveModal'));
            modal.hide();
            this.loadLeaveData();
            
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    viewLeave(leaveId) {
        const leave = Database.getById('leaves', leaveId);
        if (!leave) {
            window.samApp.showAlert('طلب الإجازة غير موجود', 'danger');
            return;
        }
        
        this.showLeaveDetails(leave);
    }

    showLeaveDetails(leave) {
        const modal = new bootstrap.Modal(document.getElementById('leaveDetailsModal'));
        const content = document.getElementById('leaveDetailsContent');
        const actions = document.getElementById('leaveActions');
        
        const employee = Database.getEmployee(leave.employee_id);
        const leaveType = Database.getAll('leave_types').find(type => type.id === leave.leave_type);
        const days = this.calculateDaysBetween(leave.start_date, leave.end_date);
        
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات الموظف</h6>
                    <p><strong>الاسم:</strong> ${employee?.name || 'غير معروف'}</p>
                    <p><strong>الرقم الوظيفي:</strong> ${employee?.employee_number || ''}</p>
                    <p><strong>القسم:</strong> ${employee?.department || 'غير محدد'}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">تفاصيل الإجازة</h6>
                    <p><strong>نوع الإجازة:</strong> ${leaveType?.name || 'غير محدد'}</p>
                    <p><strong>تاريخ البداية:</strong> ${window.samApp.formatDate(leave.start_date)}</p>
                    <p><strong>تاريخ النهاية:</strong> ${window.samApp.formatDate(leave.end_date)}</p>
                    <p><strong>عدد الأيام:</strong> ${days} يوم</p>
                    <p><strong>الحالة:</strong> ${this.getStatusBadge(leave.status)}</p>
                </div>
                <div class="col-12">
                    <h6 class="text-primary">سبب الإجازة</h6>
                    <p>${leave.reason || 'غير محدد'}</p>
                </div>
                ${leave.admin_notes ? `
                <div class="col-12">
                    <h6 class="text-primary">ملاحظات الإدارة</h6>
                    <p>${leave.admin_notes}</p>
                </div>
                ` : ''}
            </div>
        `;
        
        // Add action buttons based on leave status
        if (leave.status === 'pending') {
            actions.innerHTML = `
                <button type="button" class="btn btn-success me-2" onclick="leaveManager.approveLeave('${leave.id}')">
                    <i class="fas fa-check me-2"></i>موافقة
                </button>
                <button type="button" class="btn btn-danger" onclick="leaveManager.rejectLeave('${leave.id}')">
                    <i class="fas fa-times me-2"></i>رفض
                </button>
            `;
        } else {
            actions.innerHTML = '';
        }
        
        modal.show();
    }

    approveLeave(leaveId) {
        try {
            Database.update('leaves', leaveId, { status: 'approved' });
            window.samApp.showAlert('تم الموافقة على طلب الإجازة', 'success');
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('leaveDetailsModal'));
            modal.hide();
            this.loadLeaveData();
            
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    rejectLeave(leaveId) {
        const reason = prompt('يرجى إدخال سبب الرفض:');
        if (reason) {
            try {
                Database.update('leaves', leaveId, { 
                    status: 'rejected',
                    admin_notes: reason
                });
                window.samApp.showAlert('تم رفض طلب الإجازة', 'success');
                
                const modal = bootstrap.Modal.getInstance(document.getElementById('leaveDetailsModal'));
                modal.hide();
                this.loadLeaveData();
                
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    deleteLeave(leaveId) {
        if (confirm('هل أنت متأكد من حذف طلب الإجازة؟')) {
            try {
                Database.delete('leaves', leaveId);
                window.samApp.showAlert('تم حذف طلب الإجازة بنجاح', 'success');
                this.loadLeaveData();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    exportLeaves() {
        const leaves = Database.getLeaves();
        const employees = Database.getEmployees();
        const leaveTypes = Database.getAll('leave_types');
        
        const exportData = leaves.map(leave => {
            const employee = employees.find(emp => emp.id === leave.employee_id);
            const leaveType = leaveTypes.find(type => type.id === leave.leave_type);
            const days = this.calculateDaysBetween(leave.start_date, leave.end_date);
            
            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'نوع الإجازة': leaveType?.name || 'غير محدد',
                'تاريخ البداية': leave.start_date,
                'تاريخ النهاية': leave.end_date,
                'عدد الأيام': days,
                'الحالة': leave.status,
                'سبب الإجازة': leave.reason || '',
                'ملاحظات الإدارة': leave.admin_notes || '',
                'تاريخ الطلب': leave.created_at
            };
        });
        
        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الإجازات');
        XLSX.writeFile(wb, `leaves_${new Date().toISOString().split('T')[0]}.xlsx`);
        
        window.samApp.showAlert('تم تصدير بيانات الإجازات بنجاح', 'success');
    }

    printLeaves() {
        let leaves = Database.getAll('leaves') || [];

        // Apply current filters
        if (this.selectedEmployee) {
            leaves = leaves.filter(leave => leave.employee_id === this.selectedEmployee);
        }

        if (this.selectedType) {
            leaves = leaves.filter(leave => leave.type === this.selectedType);
        }

        if (this.selectedStatus) {
            leaves = leaves.filter(leave => leave.status === this.selectedStatus);
        }

        const period = this.selectedEmployee ?
            `موظف محدد` :
            'جميع الموظفين';

        window.printManager.printLeavesReport(leaves, period);
    }
}

// Global reference for modal actions
let leaveManager;
